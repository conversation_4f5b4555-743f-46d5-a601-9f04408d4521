"use client";
import { useAuth } from "@clerk/nextjs";
import { sdkSetUser } from "@clincove-eng/backend-sdk-temp";
import {
  matchQuery,
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import type { PropsWithChildren } from "react";
import { useMemo } from "react";

import { CLERK_TOKEN_TEMPLATE } from "@/lib/constants";
import { isArrayOfQueryKeys } from "@/utils/react-query";

import { useClerkToken } from "./clerk-token";

export default function ReactQueryProvider({ children }: PropsWithChildren) {
  const { isLoaded } = useClerkToken();
  const { getToken } = useAuth();

  const queryClient = useMemo(() => {
    const refreshToken = async () => {
      try {
        const newToken = await getToken({ template: CLERK_TOKEN_TEMPLATE });
        if (newToken) {
          useClerkToken.getState().setToken(newToken);
          sdkSetUser(newToken);
        }
      } catch {
        // Silently handle token refresh errors
      }
    };

    return new QueryClient({
      defaultOptions: {
        queries: {
          enabled: isLoaded,
          refetchOnWindowFocus: false,
          retry: 1,
          retryDelay: (attemptIndex) => {
            // Start token refresh - we can't await here but the next attempt will use the refreshed token
            void refreshToken();
            // Use exponential backoff with a minimum of 1 second delay to allow token refresh
            return Math.max(1000, Math.min(1000 * 2 ** attemptIndex, 30000));
          },
        },
        mutations: {
          retry: 1,
          retryDelay: (attemptIndex) => {
            // Start token refresh - we can't await here but the next attempt will use the refreshed token
            void refreshToken();
            // Use exponential backoff with a minimum of 1 second delay to allow token refresh
            return Math.max(1000, Math.min(1000 * 2 ** attemptIndex, 30000));
          },
        },
      },
      queryCache: new QueryCache({
        onError: () => {
          void refreshToken();
        },
      }),
      mutationCache: new MutationCache({
        onError: (_error, _variables, _context, mutation) => {
          // Only refresh token and handle error if this is the final attempt
          // This prevents the global onError from being called during retries
          const retryCount =
            typeof mutation.options.retry === "number"
              ? mutation.options.retry
              : 0;

          if (
            !mutation.state.failureCount ||
            mutation.state.failureCount >= retryCount
          ) {
            void refreshToken();
          }
        },
        onSuccess: async (_data, _variables, _context, mutation) => {
          if (mutation.meta?.invalidates) {
            const invalidateKeys = isArrayOfQueryKeys(mutation.meta.invalidates)
              ? mutation.meta.invalidates
              : [mutation.meta.invalidates];

            queryClient.invalidateQueries({
              predicate: (query) =>
                invalidateKeys.some((queryKey) =>
                  matchQuery({ queryKey }, query),
                ) ?? false,
            });
          }

          if (mutation.meta?.awaits) {
            const awaitKeys = isArrayOfQueryKeys(mutation.meta.awaits)
              ? mutation.meta.awaits
              : [mutation.meta.awaits];
            await queryClient.invalidateQueries({
              predicate: (query) =>
                awaitKeys.some((queryKey) => matchQuery({ queryKey }, query)) ??
                false,
            });
          }
        },
      }),
    });
  }, [isLoaded, getToken]);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
