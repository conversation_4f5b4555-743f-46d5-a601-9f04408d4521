import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteUsers = (
  search: string,
  type?: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-users", search, type],
    queryFn: ({ pageParam = 1 }) =>
      api.users.list({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, type },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
