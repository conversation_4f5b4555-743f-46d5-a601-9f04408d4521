import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteGroups = (
  search: string,
  type?: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-groups", type],
    queryFn: ({ pageParam = 1 }) =>
      api.groups.list({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, type },
      }),
    enabled: !!type,
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
