import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteAssignableUsers = (
  search: string,
  studyId: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-local-assignable-users", search, studyId],
    queryFn: ({ pageParam = 1 }) =>
      api.studies.getAssignableUsers({
        studyId: studyId,
        params: {
          page: pageParam,
          take: initialPageSize,
          filter: { name: search },
        },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
