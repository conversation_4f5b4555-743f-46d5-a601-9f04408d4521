import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useParams } from "next/navigation";

import { USE_SITE_CONTACTS_QUERY_KEY } from "@/components/features/sites/site-detail/tabs/hooks/use-site-contacts";
import { USE_SPONSOR_CONTACTS } from "@/components/features/sponsors/sponsor-detail/hooks/use-contacts";
import { contactKeys } from "@/components/features/studies/study-detail/tabs/contacts/hooks/use-contacts-queries";
import api from "@/lib/apis";
import type { UpdateContactPayload } from "@/lib/apis/contacts";

export const useUpdateContact = () => {
  const params = useParams();
  const studyId = params.id as string;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateContactPayload & { id: string }) => {
      const { id, ...rest } = payload;
      return api.contacts.update(id, rest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USE_SPONSOR_CONTACTS] });
      queryClient.invalidateQueries({
        queryKey: contactKeys.allLists(studyId),
      });
      queryClient.invalidateQueries({
        queryKey: [USE_SITE_CONTACTS_QUERY_KEY],
      });
    },
  });
};
