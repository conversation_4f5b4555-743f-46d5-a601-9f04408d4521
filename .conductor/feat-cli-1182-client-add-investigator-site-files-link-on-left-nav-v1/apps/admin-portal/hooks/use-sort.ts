import { parseAsString, useQueryState } from "nuqs";
import { useCallback, useEffect } from "react";

import type { SortMetadataParams } from "@/lib/apis/types";

type SortDirection = null | "asc" | "desc" | "ASC" | "DESC";

export const useSort = ({
  orderBy: defaultOderBy,
  orderDirection: defaultOrderDirection,
}: SortMetadataParams = {}) => {
  const [orderBy, setOrderBy] = useQueryState("orderBy", parseAsString);

  const [orderDirection, setOrderDirection] = useQueryState(
    "orderDirection",
    parseAsString,
  );

  const changeSort = useCallback(
    (sortBy?: string, sortOrder?: SortDirection) => {
      setOrderBy(sortBy || null);
      setOrderDirection(sortOrder || null);
    },
    [setOrderBy, setOrderDirection],
  );

  const toggleDirection = (direction?: SortDirection) => {
    setOrderDirection(direction || orderDirection === "asc" ? "desc" : "asc");
  };

  useEffect(() => {
    if (!defaultOderBy || !defaultOrderDirection) return;
    changeSort(defaultOderBy, defaultOrderDirection);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    orderBy,
    orderDirection: orderDirection as SortDirection,
    changeSort,
    toggleDirection,
  };
};
