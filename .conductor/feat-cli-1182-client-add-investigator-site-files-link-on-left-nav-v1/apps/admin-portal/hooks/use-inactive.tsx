"use client";
import { useAuth } from "@clerk/nextjs";
import { useEffect } from "react";

import { configs } from "@/lib/config";
import { clearAllStores } from "@/utils/clear-stores";

const timeOut = configs.INACTIVITY_TIMEOUT
  ? Number(configs.INACTIVITY_TIMEOUT) * 60 * 1000
  : 30 * 60 * 1000; // default inactivity timeout is 30 minutes

export const useInactivity = () => {
  const { signOut } = useAuth();

  const handleInactivityTimeout = async () => {
    await signOut();
    clearAllStores();
  };

  useEffect(() => {
    if (typeof window === "undefined") return;
    let timer: ReturnType<typeof setTimeout> | undefined;

    const resetTimer = () => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        handleInactivityTimeout();
      }, timeOut);
    };

    document.addEventListener("mousemove", resetTimer);
    document.addEventListener("click", resetTimer);
    document.addEventListener("keypress", resetTimer);
    document.addEventListener("scroll", resetTimer);
    window.addEventListener("load", resetTimer);

    return () => {
      clearTimeout(timer);
      document.removeEventListener("mousemove", resetTimer);
      document.removeEventListener("click", resetTimer);
      document.removeEventListener("keypress", resetTimer);
      document.removeEventListener("scroll", resetTimer);
      window.removeEventListener("load", resetTimer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timeOut]);

  return null;
};
