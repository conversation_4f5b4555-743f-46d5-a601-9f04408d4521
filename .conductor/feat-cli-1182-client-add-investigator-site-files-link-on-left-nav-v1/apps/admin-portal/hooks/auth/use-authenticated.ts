"use client";
import { useAuth } from "@clerk/nextjs";
import { admin, sdkSetURL, sdkSetUser } from "@clincove-eng/backend-sdk-temp";
import { useQuery } from "@tanstack/react-query";

import { useClerkToken } from "@/contexts/clerk-token";
import { configs } from "@/lib/config";

export const AUTHENTICATED_QUERY_KEY = ["authenticated"];

export const useAuthenticated = () => {
  const { getToken, isLoaded } = useAuth();
  const { setToken, setIsLoaded } = useClerkToken();

  return useQuery({
    queryKey: AUTHENTICATED_QUERY_KEY,
    queryFn: async () => {
      const token = await getToken();
      if (token) {
        setToken(token);
        sdkSetUser(token);
        sdkSetURL(configs.API_URL ?? "");
        setIsLoaded(true);
      }

      return admin.Auth.isAuthenticated({
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    },
    enabled: isLoaded,
  });
};
