import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";

import { useInfiniteProcedures } from "../../studies/study-detail/tabs/procedures/hooks/use-procedures-queries";

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const schema = z.object({
  permissionId: z.string({ required_error: "Procedure is required" }),
});

type FormValues = z.infer<typeof schema>;

export const PermissionModal = ({ isOpen, onClose }: Props) => {
  async function onSubmit(data: FormValues) {
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Permission</Modal.Header>
      <Modal.Body>
        <Form mode="onChange" schema={schema} onSubmit={onSubmit}>
          <div className="grid grid-cols-1 gap-6">
            <div className="flex flex-col gap-2">
              <Label htmlFor="permissionId">Permission</Label>
              <LazySelect
                id="permissionId"
                name="permissionId"
                placeholder="Select a permission..."
                searchPlaceholder="Search permission..."
                useInfiniteQuery={useInfiniteProcedures}
                getOptionLabel={(option) => option.name}
                getOptionValue={(option) => option.id}
                params={[]}
              />
            </div>

            <div className="flex flex-col justify-end gap-5 border-none pt-0 sm:flex-row">
              <CloseButton onClose={onClose} />
              <Button
                type="submit"
                variant="primary"
                // isLoading={isAdding}
              >
                Add
              </Button>
            </div>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
