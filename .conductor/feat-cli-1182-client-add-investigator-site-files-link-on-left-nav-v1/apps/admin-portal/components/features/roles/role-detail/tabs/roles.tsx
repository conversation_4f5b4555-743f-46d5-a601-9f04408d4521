import { useParams } from "next/navigation";
import React, { useMemo } from "react";

import { Table, TableLoading } from "@/components/ui/table";
import { Permission } from "@/lib/apis/roles";

import { useTogglePermission } from "../../hooks/use-roles-mutations";
import {
  usePermissions,
  useRole,
  useSubjects,
} from "../../hooks/use-roles-queries";
import { generateNormalPermissionColumns } from "./columns";

export const RoleTab = () => {
  const roleId = useParams().id as string;

  const { mutateAsync } = useTogglePermission(roleId);
  const { data: role, isPending: isPendingRoleDetail } = useRole(roleId);

  const { data: subjects, isPending: isPendingSubjects } = useSubjects();
  const { data: permissions, isPending: isPendingPermissions } =
    usePermissions();

  const handleClick = ({
    id,
    isAdd,
    newPermission,
  }: {
    id: string;
    isAdd: boolean;
    newPermission: Permission;
  }) => {
    mutateAsync({
      isAdd,
      roleId,
      permissionId: id,
      newPermission,
    });
  };

  const formattedPermissionSubject = useMemo(() => {
    if (!role || !subjects || !permissions) return [];

    return subjects.results
      .map((subject) => ({
        ...subject,
        type: role.type,
        selectedPermission: role.permissions,
        allPermissions: permissions.results.filter(
          (p) =>
            p.permissionSubject.id === subject.id && !p.action.endsWith("all"),
        ),
      }))
      .filter(
        (sub) =>
          !sub.allPermissions.every((per) => !per.isActive || per.isAdmin),
      );
  }, [role, subjects, permissions]);

  const columns = useMemo(
    () => generateNormalPermissionColumns(handleClick),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  return (
    <div className="overflow-hidden rounded-lg border bg-white dark:border-gray-700 dark:bg-gray-800">
      {isPendingRoleDetail || isPendingSubjects || isPendingPermissions ? (
        <TableLoading columns={columns} />
      ) : (
        <Table columns={columns} data={formattedPermissionSubject} />
      )}
    </div>
  );
};
