"use client";

import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import {
  Checkbox,
  Form,
  FormRef,
  InputField,
  Select,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { ROLE_TYPES } from "@/lib/constants";
import { cn } from "@/lib/utils";

import { useFilterRole } from "../hooks/use-filter-role";

const filterSchema = z.object({
  name: z.string().optional(),
  type: z.string().optional(),
  isActive: z.boolean().optional(),
});

type FilterFormValues = z.infer<typeof filterSchema>;

export const RoleFilter = () => {
  const [open, setOpen] = useState(false);
  const { name, type, isActive } = useFilterRole();

  const filters = [!!name, !!type, !!isActive];
  const totalFilters = filters.filter(Boolean).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button variant="outline" className="relative !py-2">
          <ListFilter className="size-5" />
          {!!totalFilters && (
            <i className="bg-primary-600 absolute right-0 top-0 block size-5 rounded-full px-1 not-italic text-white md:right-4">
              {totalFilters}
            </i>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

const FilterContent = ({ setOpen }: { setOpen: (open: boolean) => void }) => {
  const ref = useRef<FormRef<typeof filterSchema>>(null);

  const { name, type, isActive, setName, setType, goToPage, setIsActive } =
    useFilterRole();

  function onSubmit(data: FilterFormValues) {
    setName(data.name || null);
    setType(data.type || null);
    setIsActive(data.isActive || null);
    goToPage(1);
    setOpen(false);
  }

  function onClear() {
    setName(null);
    setType(null);
    setIsActive(null);
    goToPage(1);
    ref.current?.formHandler?.reset();
    setOpen(false);
  }

  const defaultValues = {
    name: name || "",
    type: type || "",
    isActive: isActive || false,
  };

  const hasFilters = !!name || !!type || !!isActive;

  return (
    <div className="">
      <Form
        schema={filterSchema}
        mode="onChange"
        onSubmit={onSubmit}
        className="w-80 md:w-96"
        ref={ref}
        defaultValues={defaultValues}
        key={Object.values(defaultValues).join("-")}
      >
        <div className="flex flex-col divide-y dark:divide-gray-500">
          {/* Role Name */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="name"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Role Name
            </Label>
            <InputField
              id="name"
              name="name"
              className="h-fit w-full"
              placeholder="Search by name..."
            />
          </div>

          {/* Role Type */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="type"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Role Type
            </Label>
            <Select
              id="type"
              name="type"
              className="h-fit w-full"
              options={ROLE_TYPES.map((roleType) => ({
                label: roleType.toUpperCase(),
                value: roleType,
              }))}
              placeholder="Select type..."
            />
          </div>

          {/* Active Status */}
          <div className="flex items-center justify-between gap-x-2 px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="isActive"
              className="text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Active
            </Label>
            <Checkbox className="size-[18px]" name="isActive" id="isActive" />
          </div>
        </div>

        <div className="flex justify-end gap-5 px-[15px] py-2 md:pb-5 md:pt-2.5">
          <Button
            variant="outline"
            className={cn(
              "w-full justify-center",
              !hasFilters && "pointer-events-none invisible",
            )}
            onClick={onClear}
            type="button"
          >
            Clear Filters
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="w-full justify-center"
          >
            Apply Filters
          </Button>
        </div>
      </Form>
    </div>
  );
};
