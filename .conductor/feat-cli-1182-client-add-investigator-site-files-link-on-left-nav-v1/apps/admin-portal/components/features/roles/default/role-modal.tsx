import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { Role } from "@/lib/apis/roles";
import { ROLE_TYPES } from "@/lib/constants";
import { capitalize } from "@/utils/string";

import { useAddRole, useUpdateRole } from "../hooks/use-roles-mutations";

export const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  type: z.enum(ROLE_TYPES, {
    message: "Type is required",
  }),
  description: z
    .string({ required_error: "Description is required" })
    .min(1, "Description is required"),
  isActive: z.boolean().default(true),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedRole?: Role;
};

export type RoleType = (typeof ROLE_TYPES)[number];

export const RoleModal = ({ isOpen, onClose, selectedRole }: Props) => {
  const { mutateAsync: addRole, isPending: isAdding } = useAddRole();
  const { mutateAsync: updateRole, isPending: isUpdating } = useUpdateRole(
    selectedRole?.id as string,
  );

  const isEditing = !!selectedRole;

  async function onSubmit(data: z.infer<typeof schema>) {
    isEditing
      ? await updateRole({ ...data, id: selectedRole.id })
      : await addRole(data);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>{isEditing ? "Edit" : "Add"} Role</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          defaultValues={{
            name: selectedRole?.name || "",
            description: selectedRole?.description || "",
            isActive:
              typeof selectedRole?.isActive === "boolean"
                ? selectedRole.isActive
                : true,
            type: selectedRole?.type || "",
          }}
          formProps={{ shouldFocusError: false }}
        >
          <div className="space-y-4">
            {selectedRole?.type !== "admin" && (
              <div className=" flex flex-col gap-2">
                <Label htmlFor="name">Name</Label>
                <InputField
                  id="name"
                  name="name"
                  placeholder="Enter role name"
                />
              </div>
            )}

            {selectedRole?.type !== "admin" && (
              <div className=" flex flex-col gap-2">
                <Label htmlFor="type">Type</Label>
                <Select
                  id="type"
                  name="type"
                  placeholder="Select a type"
                  className="capitalize"
                  options={ROLE_TYPES.filter((type) => type !== "admin").map(
                    (type) => ({
                      label: capitalize(type),
                      value: type,
                    }),
                  )}
                />
              </div>
            )}

            <div className=" flex flex-col gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description"
              />
            </div>

            {isEditing && (
              <div className="flex items-center gap-2">
                <Checkbox id="isActive" name="isActive" />
                <Label htmlFor="isActive">Active</Label>
              </div>
            )}

            <div className="flex flex-col justify-end gap-4 border-none pt-0 sm:flex-row sm:gap-5">
              <CloseButton onClose={onClose} />
              <Button
                type="submit"
                variant="primary"
                disabled={isAdding || isUpdating}
                isLoading={isAdding || isUpdating}
              >
                Save
              </Button>
            </div>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
