"use client";

import { HiExclamationTriangle } from "react-icons/hi2";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { PromptTemplateTab, PromptVariableTab } from "./tabs";

const PROMPT_TABS = [
  {
    title: "Prompt Templates",
    key: "templates",
    content: <PromptTemplateTab />,
  },
  {
    title: "Prompt Variables",
    key: "variables",
    content: <PromptVariableTab />,
  },
];

const BREADCRUMB_ITEMS = [{ label: "App Prompts" }];

export const ProtocolPromptsContent = () => {
  return (
    <>
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>App Prompts</PageHeader>
      <div className="flex items-center gap-3 rounded-lg border border-amber-300 bg-amber-100 px-4 py-3 text-sm text-amber-800 dark:border-amber-800/30 dark:bg-amber-900/20 dark:text-amber-300">
        <HiExclamationTriangle className="size-6 flex-shrink-0 text-amber-600 dark:text-amber-400" />
        <span className="font-medium sm:text-lg">
          This configuration is centrally managed and cannot be edited here.
        </span>
      </div>
      <TabsWrapper tabs={PROMPT_TABS} />
    </>
  );
};
