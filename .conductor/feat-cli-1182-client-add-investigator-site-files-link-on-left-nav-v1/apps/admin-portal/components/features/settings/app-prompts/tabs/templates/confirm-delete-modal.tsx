import React from "react";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";

import { ExtendedPrompt } from ".";
import { useDeletePrompt } from "./hooks/use-protocol-prompt-mutations";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedPrompt: ExtendedPrompt;
};

export const ConfirmDeleteModal = ({
  isOpen,
  onClose,
  selectedPrompt,
}: Props) => {
  const { mutateAsync: deletePrompt, isPending: isDeleting } =
    useDeletePrompt();

  const handleDelete = async () => {
    await deletePrompt(selectedPrompt.id);
    onClose();
  };

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title="Delete">
      <p className="dark:text-white">
        Are you sure you want to delete{" "}
        <span className="font-semibold">{selectedPrompt.name}</span>?
      </p>
      <div className="mt-4 flex flex-col justify-end gap-4 sm:col-span-2 sm:flex-row">
        <CloseButton onClose={onClose} />
        <Button onClick={handleDelete} variant="primary" isLoading={isDeleting}>
          Save
        </Button>
      </div>
    </WrapperModal>
  );
};
