import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { Monaco } from "@monaco-editor/react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Checkbox, Form, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import {
  MonacoEditor,
  MonacoEditorInstance,
} from "@/components/ui/form/monaco-editor";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfinitePromptVariables } from "@/hooks/queries/use-infinite-prompt-variables";
import { useDebounce } from "@/hooks/use-debounce";
import { PromptVariable } from "@/lib/apis/prompt-variables";
import { capitalize } from "@/utils/string";

import { ChatMessageWithId } from "./prompt-modal";
export const ROLE_MESSAGE_TYPES = ["user", "model"] as const;

const messageSchema = z.object({
  message: z
    .string({
      required_error: "Message is required",
      invalid_type_error: "Message is required",
    })
    .min(1, "Message is required"),
  role: z.enum(ROLE_MESSAGE_TYPES, {
    errorMap: () => ({
      message: "Role is required",
    }),
  }),
  isCacheable: z.boolean().default(false),
});

export type ChatMessage = z.infer<typeof messageSchema>;

type AddMessageModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSave: (
    chatMessage: ChatMessage & {
      id?: number;
    },
  ) => void;
  messages: ChatMessage[];
  selectedMessage: ChatMessageWithId | null;
};

export const AddMessageModal = ({
  isOpen,
  onClose,
  onSave,
  messages,
  selectedMessage,
}: AddMessageModalProps) => {
  const [search, setSearch] = useState("");
  // const [previewMessage, setPreviewMessage] = useState("");
  // const { isOpen: isOpenPreviewModal, close, open } = useDisclosure();

  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<MonacoEditorInstance | null>(null);
  const monaco = useRef<Monaco | null>(null);

  const debouncedSearch = useDebounce(search);
  const {
    data,
    isPending,
    isPlaceholderData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfinitePromptVariables(debouncedSearch, {
    isActive: true,
    published: true,
  });

  const formHandler = useForm<ChatMessage>({
    mode: "onChange",
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: selectedMessage?.message || "",
      role: selectedMessage?.role,
      isCacheable: selectedMessage?.isCacheable || false,
    },
  });

  // const template = formHandler.watch("message")?.trim();

  const isEditing = !!selectedMessage;

  const isDisabledUserRole = !messages.length;

  const validRole = isDisabledUserRole
    ? ROLE_MESSAGE_TYPES.filter((role) => role === "user")
    : ROLE_MESSAGE_TYPES;

  const variables = data?.pages.flatMap((page) => page.results);

  const handleInsertVariable = (variable: PromptVariable) => {
    const position = editorRef.current?.getPosition();
    if (monaco.current && editorRef.current) {
      const formattedKey = `<%= ${variable.key} %>`;
      const range = new monaco.current.Range(
        position?.lineNumber || 1,
        position?.column || 1,
        position?.lineNumber || 1,
        position?.column || 1,
      );
      editorRef.current.executeEdits("insert-variable", [
        {
          range: range,
          text: formattedKey,
          forceMoveMarkers: true,
        },
      ]);

      editorRef.current.setPosition({
        lineNumber: position?.lineNumber || 1,
        column: (position?.column || 1) + formattedKey.length,
      });
      editorRef.current.focus();
    } else {
      const currentMessage = formHandler.getValues("message");
      const isLastCharSpace =
        !currentMessage?.length ||
        currentMessage?.[currentMessage?.length - 1] === " ";
      const formattedKey = isLastCharSpace
        ? `<%= ${variable.key} %>`
        : ` <%= ${variable.key} %>`;
      formHandler.setValue(
        "message",
        currentMessage ? `${currentMessage}${formattedKey}` : formattedKey,
        {
          shouldValidate: true,
        },
      );
    }
  };

  // const handlePreviewMessage = () => {
  //   try {
  //     const currentMessage = formHandler.getValues("message");
  //     const defaultVariables =
  //       variables?.reduce(
  //         (acc, variable) =>
  //           currentMessage.includes(variable.key)
  //             ? merge(acc, JSON.parse(variable.fallbackValue))
  //             : acc,
  //         {},
  //       ) ?? {};

  //     const isExistsDefaultValue = !!Object.keys(defaultVariables).length;
  //     const result = ejs.render(
  //       currentMessage || "",
  //       isExistsDefaultValue ? defaultVariables : undefined,
  //     );
  //     setPreviewMessage(result);
  //     open();
  //   } catch {
  //     toast.error("Fail to preview template");
  //   }
  // };

  const handleSubmit = (data: z.infer<typeof messageSchema>) => {
    onSave({ ...data, id: selectedMessage?.id });
    onClose();
  };

  const renderVariables = () => {
    if (isPending)
      return Array(5)
        .fill(0)
        .map((_, i) => <Skeleton key={i} className="h-8 w-12" />);
    if (variables?.length)
      return variables?.map((variable) => (
        <button
          type="button"
          className="rounded-md bg-gray-300 px-2  py-1 text-black dark:bg-gray-500 dark:text-white "
          key={variable.id}
          onClick={() => handleInsertVariable(variable)}
        >
          {variable.key}
        </button>
      ));

    return (
      <div className="flex-1 py-1 text-center text-sm text-gray-500 dark:text-gray-400">
        No variables found
      </div>
    );
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const scrollContainer = scrollContainerRef.current;
      const target = observerTarget.current;

      if (!scrollContainer || !target) return;

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (
            entry.isIntersecting &&
            !isFetchingNextPage &&
            hasNextPage &&
            !debouncedSearch
          ) {
            fetchNextPage();
          }
        },
        {
          root: scrollContainer,
          rootMargin: "0px",
          threshold: 0.1,
        },
      );

      observer.observe(target);

      return () => {
        observer.disconnect();
      };
    }, 300);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, debouncedSearch]);

  return (
    <>
      <WrapperModal
        isOpen={isOpen}
        className="[&>div]:max-w-3xl"
        onClose={onClose}
        title={isEditing ? "Edit Message" : "New Message"}
      >
        <Form
          className="space-y-4"
          onSubmit={handleSubmit}
          formMethods={formHandler}
          schema={messageSchema}
        >
          <div className="space-y-1">
            <Label htmlFor="role">Role</Label>
            <Select
              id="role"
              name="role"
              placeholder="Select a role"
              options={validRole.map((type) => ({
                label: capitalize(type),
                value: type,
              }))}
            />
          </div>
          <div className="space-y-1">
            <Label>Variables</Label>

            <div
              ref={scrollContainerRef}
              className="relative flex max-h-[200px] flex-col overflow-y-auto rounded-md border  dark:border-gray-400"
            >
              <div className="sticky top-0 z-10 bg-white p-2 dark:bg-gray-800">
                <input
                  name="search"
                  type="text"
                  placeholder="Search..."
                  className=" w-full rounded-md border-gray-300 px-2.5 py-1.5 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </div>
              <LoadingWrapper className="flex-1" isLoading={isPlaceholderData}>
                <div className="flex flex-1 flex-wrap gap-2 p-2">
                  {renderVariables()}
                </div>
              </LoadingWrapper>
              <div ref={observerTarget} className="h-px" aria-hidden="true" />
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <Label htmlFor="message">Message</Label>{" "}
            </div>
            <MonacoEditor
              options={{
                // acceptSuggestionOnCommitCharacter: false,
                // acceptSuggestionOnEnter: "off",
                // suggestOnTriggerCharacters: false,
                // snippetSuggestions: "none",
                quickSuggestions: false,
              }}
              height={250}
              language="html"
              name="message"
              editorRef={editorRef}
              monacoRef={monaco}
            />
          </div>

          <div className="flex items-center gap-2">
            <Label htmlFor="isCacheable">Cacheable</Label>
            <Checkbox id="isCacheable" name="isCacheable" />
          </div>

          <div className="flex justify-end gap-4">
            {/* <Button
              onClick={handlePreviewMessage}
              type="button"
              variant="primary"
              disabled={!template}
            >
              Preview
            </Button> */}
            <CloseButton onClose={onClose} />
            <Button type="submit" variant="primary">
              Save
            </Button>
          </div>
        </Form>
      </WrapperModal>

      {/* <PreviewPromptTemplate
        template={previewMessage}
        isOpen={isOpenPreviewModal}
        onClose={close}
      /> */}
    </>
  );
};
