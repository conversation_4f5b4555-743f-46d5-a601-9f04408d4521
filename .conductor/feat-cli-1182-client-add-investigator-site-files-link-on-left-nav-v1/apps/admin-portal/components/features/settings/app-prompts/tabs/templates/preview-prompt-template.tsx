import React from "react";

import { CloseButton } from "@/components/ui/button";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  template: string;
};

export const PreviewPromptTemplate = ({ isOpen, onClose, template }: Props) => {
  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title="Preview Template">
      <p className="whitespace-pre-wrap dark:text-white">{template}</p>
      <div className="mt-4 flex justify-end gap-4">
        <CloseButton onClose={onClose} />
      </div>
    </WrapperModal>
  );
};
