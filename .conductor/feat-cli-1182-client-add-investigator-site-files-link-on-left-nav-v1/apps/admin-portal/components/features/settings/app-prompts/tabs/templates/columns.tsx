import { type ColumnDef } from "@tanstack/react-table";
import { FiSearch } from "react-icons/fi";
import { IoMdCopy } from "react-icons/io";
import { MdPowerSettingsNew } from "react-icons/md";

import {
  TableGenericButton,
  TableRemoveButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { Prompt } from "@/lib/apis/app-prompts";

const MODEL_PROVIDER_LABEL = {
  openai: "OpenAi",
  gemini: "Gemini",
};

export const generatePromptColumns = ({
  onActivate,
  onView,
  onTest,
}: {
  onView: (data: Prompt) => void;
  onActivate: (data: Prompt) => void;
  onTest: () => void;
}): ColumnDef<Prompt>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => {
      return (
        <button
          onClick={() => onView(row.original)}
          className="text-primary-500 text-left hover:underline"
        >
          {row.getValue("name")}
        </button>
      );
    },
  },
  {
    header: "Type",
    cell: ({ row }) => {
      return (
        <span className="capitalize">{row.original.key.replace("_", " ")}</span>
      );
    },
  },
  {
    header: "Model Provider",
    cell: ({ row }) => {
      return MODEL_PROVIDER_LABEL[row.original.modelProvider];
    },
  },
  {
    accessorKey: "model",
    header: "Model",
  },
  {
    accessorKey: "version",
    header: "Version",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      return <ActiveStatusBadge isActive={row.original.isActive} />;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableViewButton type="button" onClick={() => onView(data)} />
          <TableGenericButton onClick={onTest} type="button">
            Test <FiSearch className="size-4" />
          </TableGenericButton>
          {/* {!data.isActive && (
            <TableGenericButton onClick={() => onActivate(data)} type="button">
              Activate <MdPowerSettingsNew className="size-4" />
            </TableGenericButton>
          )} */}
        </div>
      );
    },
  },
];
