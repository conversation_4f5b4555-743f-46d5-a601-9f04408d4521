import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { RemoveStudyFromGroupPayload } from "@/lib/apis/groups/types";

import { USE_GROUP_STUDIES_QUERY_KEY } from "./use-group-studies";

export function useRemoveStudyFromGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: RemoveStudyFromGroupPayload) =>
      api.groups.removeStudyFromGroup(payload),
    onSuccess: (_, { groupId }) =>
      queryClient.invalidateQueries({
        queryKey: [USE_GROUP_STUDIES_QUERY_KEY, groupId],
      }),
    onSettled: (_, err) => !err && toast.success("Remove study successfully"),
    onError: (err) => toast.error(err?.message || "Failed to remove study"),
  });
}
