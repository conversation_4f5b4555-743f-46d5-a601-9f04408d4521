import { capitalize } from "lodash";
import { z } from "zod";

import {
  AddressFormFields,
  addressSchema,
} from "@/components/shared/address-form-fields";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

import { useAddGroup } from "../hooks/use-add-group";

export const GROUP_INSTITUTION_TYPES = [
  "organization",
  "cro",
  "site",
  "sponsor",
  "family",
  "patient",
] as const;

export const GROUP_TYPES = ["site", "cro", "clincove"] as const;

export const addGroupSchema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  phone: z
    .string({ required_error: "Phone is required" })
    .min(1, "Phone is required"),
  email: z
    .string({ required_error: "Email is required" })
    .email("Invalid email")
    .min(1, "Email is required"),
  type: z.enum(GROUP_TYPES, {
    required_error: "Type is required",
  }),
  groupInstitutionType: z.enum(GROUP_INSTITUTION_TYPES, {
    required_error: "Institution type is required",
  }),
  address: addressSchema,
});

type ModalAddGroupProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddGroup = ({ isOpen, onClose }: ModalAddGroupProps) => {
  const { mutateAsync: addGroup, isPending: isAdding } = useAddGroup();

  async function onSubmit(data: z.infer<typeof addGroupSchema>) {
    await addGroup({
      ...data,
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Group</Modal.Header>
      <Modal.Body className="p-4 ">
        <Form
          mode="onChange"
          schema={addGroupSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <GroupForm onClose={onClose} isSubmitting={isAdding} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type GroupFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

export const GroupForm = ({ onClose, isSubmitting }: GroupFormProps) => {
  return (
    <>
      <div className="grid flex-1 gap-3 overflow-y-auto sm:grid-cols-2 sm:gap-6">
        <div>
          <Label htmlFor="name">Name</Label>
          <InputField id="name" name="name" placeholder="Enter name" />
        </div>
        <div>
          <Label htmlFor="groupInstitutionType">Institution Type</Label>
          <Select
            id="groupInstitutionType"
            name="groupInstitutionType"
            placeholder="Select institution type"
            options={GROUP_INSTITUTION_TYPES.map((type) => ({
              label: capitalize(type),
              value: type,
            }))}
          />
        </div>
        <div>
          <Label htmlFor="phone">Phone</Label>
          <InputField id="phone" name="phone" placeholder="Enter phone" />
        </div>
        <div>
          <Label htmlFor="email">Email</Label>
          <InputField
            id="email"
            name="email"
            type="email"
            placeholder="Enter email"
          />
        </div>
        <AddressFormFields />
      </div>
      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5 ">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
