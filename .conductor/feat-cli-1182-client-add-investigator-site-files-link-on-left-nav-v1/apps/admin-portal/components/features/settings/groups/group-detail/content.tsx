"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";

import { useGroup } from "../hooks/use-group";
import { ModalEditGroup } from "./modal-edit-group";
import { GroupTabs } from "./tabs";

export const GroupDetailContent = () => {
  const [isOpen, setIsOpen] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { data: group, isPending: isLoadingGroup } = useGroup(id);

  const breadcrumbItems = [
    { label: "Groups", href: "/settings/groups" },
    { label: group?.name ?? "Group Detail", loading: isLoadingGroup },
  ];

  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex items-center justify-between">
          <PageHeader showBackButton href="/settings/groups">
            {isLoadingGroup ? (
              <Skeleton className="h-7 w-32" />
            ) : (
              group?.name || "Group Detail"
            )}
          </PageHeader>
          <Button
            variant="primary"
            onClick={() => setIsOpen(true)}
            className="px-2 sm:px-4"
          >
            <CiEdit />
            Edit Group
          </Button>
        </div>
        {isLoadingGroup ? (
          <GroupContentSkeleton />
        ) : (
          <OverviewCard title="Overview">
            <div className="grid grid-cols-2 gap-4">
              <OverviewItem label="Name" value={group?.name ?? "N/A"} />
              <OverviewItem label="Type" value={group?.type ?? "N/A"} />
              <OverviewItem
                label="Institution Type"
                value={group?.groupInstitutionType ?? "N/A"}
              />

              <OverviewItem label="Site" value={group?.site?.name ?? "N/A"} />
              <OverviewItem
                className="col-span-2"
                label="Address"
                value={
                  group?.address
                    ? `${group.address.addressLine}, ${group.address.city}, ${group.address.stateProvince.name} ${group.address.zipPostalCode}, ${group.address.country.name}`
                    : "N/A"
                }
              />
            </div>
          </OverviewCard>
        )}
        <GroupTabs group={group} />
      </div>
      <ModalEditGroup
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        group={group}
      />
    </>
  );
};

const GroupContentSkeleton = () => {
  return (
    <div className="flex flex-col">
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <Skeleton className="mb-3 h-6 w-24" />
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
};
