import { Tabs, TabsItem } from "@/components/ui/tabs";
import type { Group } from "@/lib/apis/groups/types";

import { ProfilesTab } from "./profiles-tab";
import { StudiesTab } from "./studies-tab";

export const GroupTabs = ({ group }: { group?: Group }) => {
  return (
    <Tabs defaultValue="profiles" className="w-full">
      <TabsItem title="Profiles">
        <ProfilesTab
          groupId={group?.id ?? ""}
          isAdminGroup={group?.type === "clincove"}
        />
      </TabsItem>
      <TabsItem title="Studies">
        <StudiesTab groupId={group?.id ?? ""} />
      </TabsItem>
    </Tabs>
  );
};
