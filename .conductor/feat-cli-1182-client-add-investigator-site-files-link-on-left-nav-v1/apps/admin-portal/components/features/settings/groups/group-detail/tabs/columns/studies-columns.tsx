import type { ColumnDef } from "@tanstack/react-table";
import { MdDelete } from "react-icons/md";

import { GroupStudy } from "@/lib/apis/groups/types";

export const generateColumns = (
  onRemove: (studyId: string, siteId: string) => void,
) => {
  const columns: ColumnDef<GroupStudy>[] = [
    {
      accessorKey: "site.name",
      header: "Site",
    },
    {
      accessorKey: "study.name",
      header: "Study",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex gap-2">
            <button
              className="flex items-center gap-1 text-red-500 hover:text-red-600 disabled:cursor-not-allowed disabled:text-gray-300"
              onClick={() =>
                onRemove(row.original.studyId, row.original.siteId as string)
              }
            >
              <MdDelete className="h-4 w-4" />
              Remove
            </button>
          </div>
        );
      },
    },
  ];

  return columns;
};
