import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const USE_ELIGIBLE_USERS_QUERY_KEY = "eligible-users";

export const useInfiniteEligibleUsers = (
  search: string,
  groupId: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: [USE_ELIGIBLE_USERS_QUERY_KEY, groupId],
    queryFn: ({ pageParam = 1 }) =>
      api.groups.getEligibleUsers(groupId, {
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    enabled: !!groupId,
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
