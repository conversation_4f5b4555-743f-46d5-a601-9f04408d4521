import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddTMFRefModelPayload,
  UpdateTMFRefModelPayload,
} from "@/lib/apis/tmf-ref-models";

import { tmfRefModelKeys } from "./use-tmf-ref-model-queries";

export const useAddTMFRefModel = () =>
  useMutation({
    mutationFn: (payload: AddTMFRefModelPayload) =>
      api.tmfRefModelApi.create(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to add TMF Reference Model"),
    onSettled: (_, err) =>
      !err && toast.success("Add TMF Reference Model successfully"),
    meta: {
      awaits: tmfRefModelKeys.allLists(),
    },
  });

export const useUpdateTMFRefModel = () =>
  useMutation({
    mutationFn: (payload: UpdateTMFRefModelPayload) =>
      api.tmfRefModelApi.update(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to update TMF Reference Model"),
    onSettled: (_, err) =>
      !err && toast.success("Update TMF Reference Model successfully"),
    meta: {
      awaits: tmfRefModelKeys.allLists(),
    },
  });

export const useDeleteTMFRefModel = () =>
  useMutation({
    mutationFn: (id: string) => api.tmfRefModelApi.delete(id),
    onError: (err) =>
      toast.error(err?.message || "Fail to delete TMF Reference Model"),
    onSettled: (_, err) =>
      !err && toast.success("Delete TMF Reference Model successfully"),
    meta: {
      awaits: tmfRefModelKeys.allLists(),
    },
  });
