import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddCategoryVersionPayload,
  UpdateCategoryVersionPayload,
} from "@/lib/apis/artifact-categories";
import { AddCategorySummaryPayload } from "@/lib/apis/category-ai-summary";

import { UpdateCategorySummaryPayload } from "./../../../../../../../lib/apis/category-ai-summary/types";
import { categoryVersionKeys } from "./use-category-version-queries";

export const useAddCategoryVersion = () =>
  useMutation({
    mutationFn: (payload: AddCategoryVersionPayload) =>
      api.artifactCategories.createVersion(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to add category version"),
    onSettled: (_, err) =>
      !err && toast.success("Add category version successfully"),
    meta: {
      awaits: categoryVersionKeys.allLists(),
    },
  });

export const useUpdateCategoryVersion = () =>
  useMutation({
    mutationFn: (payload: UpdateCategoryVersionPayload) =>
      api.artifactCategories.updateVersion(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to update category version"),
    onSettled: (_, err) =>
      !err && toast.success("Update category version successfully"),
    meta: {
      awaits: categoryVersionKeys.allLists(),
    },
  });

export const useDeleteCategoryVersion = () =>
  useMutation({
    mutationFn: (id: string) => api.artifactCategories.deleteVersion(id),
    onError: (err) =>
      toast.error(err?.message || "Fail to delete category version"),
    onSettled: (_, err) =>
      !err && toast.success("Delete category version successfully"),
    meta: {
      awaits: categoryVersionKeys.allLists(),
    },
  });

export const useAddCategorySummary = () =>
  useMutation({
    mutationFn: (payload: AddCategorySummaryPayload) =>
      api.categoryAiSummary.create(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to add category summary"),
    onSettled: (_, err) =>
      !err && toast.success("Add category summary successfully"),
    meta: {
      awaits: categoryVersionKeys.allLists(),
    },
  });

export const useUpdateCategorySummary = () =>
  useMutation({
    mutationFn: (payload: UpdateCategorySummaryPayload) =>
      api.categoryAiSummary.update(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to update category summary"),
    onSettled: (_, err) =>
      !err && toast.success("Update category summary successfully"),
    meta: {
      awaits: categoryVersionKeys.allLists(),
    },
  });
