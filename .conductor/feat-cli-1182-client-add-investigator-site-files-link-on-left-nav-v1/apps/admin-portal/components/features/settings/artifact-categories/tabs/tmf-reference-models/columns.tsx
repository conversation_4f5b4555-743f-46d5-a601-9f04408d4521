import { ColumnDef } from "@tanstack/react-table";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { TMFRefModel } from "@/lib/apis/tmf-ref-models";

export const generateTmfColumns = ({
  onView,
}: {
  onView: (data: TMFRefModel) => void;
}): ColumnDef<TMFRefModel>[] => [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "tmfRefModel",
    header: "Label",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableViewButton type="button" onClick={() => onView(data)} />
        </div>
      );
    },
  },
];
