import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { TMFRefModel } from "@/lib/apis/tmf-ref-models";

import {
  useAddTMFRefModel,
  useUpdateTMFRefModel,
} from "./hooks/use-tmf-ref-model-mutations";

const schema = z.object({
  tmfRefModel: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  description: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedTMFModel: TMFRefModel | null;
};

export const TMFModal = function ({
  isOpen,
  onClose,
  selectedTMFModel,
}: Props) {
  const { mutateAsync: addTMFRefModel, isPending: isAdding } =
    useAddTMFRefModel();
  const { mutateAsync: updateTMFRefModel, isPending: isUpdating } =
    useUpdateTMFRefModel();

  const isEditing = !!selectedTMFModel;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateTMFRefModel({
          ...data,
          id: selectedTMFModel.id,
        })
      : await addTMFRefModel(data);

    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`TMF Reference Model Details`}
    >
      <Form
        defaultValues={{
          tmfRefModel: selectedTMFModel?.tmfRefModel || "",
          description: selectedTMFModel?.description || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="tmfRefModel">Label</Label>
            <InputField
              id="tmfRefModel"
              name="tmfRefModel"
              placeholder="Enter label..."
              readOnly
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
              readOnly
            />
          </div>
        </div>
      </Form>
    </WrapperModal>
  );
};
