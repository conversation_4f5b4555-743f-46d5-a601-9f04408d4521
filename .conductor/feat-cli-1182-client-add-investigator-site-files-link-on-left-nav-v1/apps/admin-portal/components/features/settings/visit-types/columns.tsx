import { type ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { MdOutlineEdit } from "react-icons/md";

import { VisitCategoryBadge } from "@/components/ui/badges";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { VisitType } from "@/lib/apis/visit-types";

export const getColumns = (
  onEdit: (visitType: VisitType) => void,
): ColumnDef<VisitType>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <span
        onClick={() => onEdit(row.original)}
        role="button"
        className="text-primary-500 hover:underline"
      >
        {row.getValue("name")}
      </span>
    ),
  },
  {
    accessorKey: "study",
    header: "Study",
    cell: ({ row }) =>
      row.original.study ? (
        <Link
          href={`/studies/${row.original.study.id}`}
          className="text-primary-500 hover:underline"
        >
          {row.original.study.name}
        </Link>
      ) : (
        "-"
      ),
  },
  {
    accessorKey: "visitCategory",
    header: "Visit Category",
    cell: ({ row }) =>
      row.original.visitCategory ? (
        <VisitCategoryBadge variant={row.original.visitCategory} />
      ) : (
        "-"
      ),
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      const isActive = row.getValue("isActive");
      if (isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const visitType = row.original;

      return (
        <div
          className="text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
          onClick={() => onEdit(visitType)}
        >
          <span className="whitespace-nowrap">Edit</span>
          <MdOutlineEdit />
        </div>
      );
    },
  },
];
