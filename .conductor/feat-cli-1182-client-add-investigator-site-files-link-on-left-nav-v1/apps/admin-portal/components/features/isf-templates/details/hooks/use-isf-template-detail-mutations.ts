import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

import api from "@/lib/apis";
import {
  CreateFilePayload,
  CreateFolderPayload,
  MoveFilePayload,
  UpdateFilePayload,
  UpdateFolderPayload,
} from "@/lib/apis/isf-templates";

import { isfTemplateDetailKeys } from "./use-isf-template-detail-queries";

export const useCreateFolder = (id: string) => {
  return useMutation({
    mutationFn: (payload: CreateFolderPayload) =>
      api.isfTemplates.createFolder({ ...payload, id }),
    onError: (error) => toast.error(error.message ?? "Failed to create folder"),
    onSettled: (_, error) =>
      !error && toast.success("Folder created successfully"),
    meta: {
      awaits: [isfTemplateDetailKeys.folders(id)],
    },
  });
};

export const useUpdateFolder = (id: string) => {
  return useMutation({
    mutationFn: (payload: UpdateFolderPayload) =>
      api.isfTemplates.updateFolder({ ...payload, id }),
    onError: (error) => toast.error(error.message ?? "Failed to update folder"),
    onSettled: (_, error) =>
      !error && toast.success("Folder updated successfully"),
    meta: {
      awaits: [isfTemplateDetailKeys.folders(id)],
    },
  });
};

export const useMoveFolder = (id: string) =>
  useMutation({
    mutationFn: (payload: {
      folderName: string;
      newParentDirectoryName?: string;
      oldParentDirectoryName?: string;
      isRollback?: boolean;
    }) => api.isfTemplates.moveFolder({ id, ...payload }),
    onError: (error, payload) =>
      payload.isRollback
        ? toast.error(error.message ?? "Failed to rollback")
        : toast.error(error.message ?? "Failed to move folder"),
    onSettled: (_, error, payload) => {
      if (error) return;
      return payload.isRollback
        ? toast.success("Rollback successful")
        : toast.success("Folder moved successfully");
    },
    meta: {
      awaits: [isfTemplateDetailKeys.folders(id)],
    },
  });

export const useDeleteFolder = (id: string) => {
  return useMutation({
    mutationFn: (payload: { folderName: string }) =>
      api.isfTemplates.deleteFolder({ id, ...payload }),
    onError: (error) => toast.error(error.message ?? "Failed to delete folder"),
    onSettled: (_, error) =>
      !error && toast.success("Folder deleted successfully"),
    meta: {
      awaits: [isfTemplateDetailKeys.folders(id)],
    },
  });
};

export const useCreateFile = (id: string) => {
  return useMutation({
    mutationFn: (payload: CreateFilePayload) =>
      api.isfTemplates.createFile({ ...payload, id }),
    onError: (error) => toast.error(error.message ?? "Failed to create file"),
    onSettled: (_, error) =>
      !error && toast.success("File created successfully"),
    meta: {
      awaits: [isfTemplateDetailKeys.allFiles(id)],
    },
  });
};

export const useMoveFile = (id: string) => {
  return useMutation({
    mutationFn: (payload: MoveFilePayload & { isRollback?: boolean }) =>
      api.isfTemplates.moveFile({ ...payload, id }),
    onError: (error, payload) =>
      payload.isRollback
        ? toast.error(error.message ?? "Failed to rollback")
        : toast.error(error.message ?? "Failed to move file"),
    onSettled: (_, error, payload) => {
      if (error) return;
      return payload.isRollback
        ? toast.success("Rollback successful")
        : toast.success("File moved successfully");
    },
    meta: {
      awaits: [
        isfTemplateDetailKeys.allFiles(id),
        isfTemplateDetailKeys.folders(id),
      ],
    },
  });
};

export const useUpdateFile = (id: string) => {
  return useMutation({
    mutationFn: (payload: UpdateFilePayload) =>
      api.isfTemplates.updateFile({ ...payload, id }),
    onError: (error) => toast.error(error.message ?? "Failed to update file"),
    onSettled: (_, error) =>
      !error && toast.success("File updated successfully"),
    meta: {
      awaits: [isfTemplateDetailKeys.allFiles(id)],
    },
  });
};

export const useDeleteFile = (id: string) => {
  return useMutation({
    mutationFn: (payload: { title: string; parentDirectoryName: string }) =>
      api.isfTemplates.deleteFile({ id, ...payload }),
    onError: (error) => toast.error(error.message ?? "Failed to delete file"),
    onSettled: (_, error) =>
      !error && toast.success("File deleted successfully"),
    meta: {
      awaits: [
        isfTemplateDetailKeys.allFiles(id),
        isfTemplateDetailKeys.folders(id),
      ],
    },
  });
};
