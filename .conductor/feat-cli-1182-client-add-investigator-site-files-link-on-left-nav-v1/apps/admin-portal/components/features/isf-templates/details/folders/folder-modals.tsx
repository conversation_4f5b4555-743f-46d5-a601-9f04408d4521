import { <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { useParams } from "next/navigation";
import React from "react";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Directory } from "@/lib/apis/isf-templates";

import {
  useCreateFolder,
  useUpdateFolder,
} from "../hooks/use-isf-template-detail-mutations";

const exportSchema = z.object({
  name: z
    .string({
      required_error: "Folder name is required",
      invalid_type_error: "Folder name is required",
    })
    .min(1, "Folder name is required"),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const FolderModal = function ({
  isOpen,
  onClose,
  selectedFolder,
}: Props & {
  selectedFolder: Directory | null;
}) {
  const id = useParams().id as string;
  const { mutateAsync: addFolder, isPending: isAdding } = useCreateFolder(id);
  const { mutateAsync: updateFolder, isPending: isEditing } =
    useUpdateFolder(id);

  const onSubmit = async (data: z.infer<typeof exportSchema>) => {
    !selectedFolder
      ? await addFolder(data)
      : await updateFolder({
          folderName: selectedFolder.name,
          newFolderName: data.name,
        });
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={!selectedFolder ? "New Folder" : "Update Folder"}
    >
      <Form
        mode="onChange"
        schema={exportSchema}
        onSubmit={onSubmit}
        defaultValues={{
          name: selectedFolder?.name || "",
        }}
      >
        <div className="flex flex-col gap-2">
          <Label htmlFor="name">Folder Name</Label>
          <InputField
            id="name"
            name="name"
            placeholder="Enter folder name..."
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            isLoading={isAdding || isEditing}
            type="submit"
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};

export const ConfirmDeleteFolderModal = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
}: Props & {
  onConfirm: () => void;
  isLoading: boolean;
}) => {
  const handleConfirmDelete = async () => {
    onConfirm();
  };

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title="Delete Folder">
      <div className="flex flex-col items-center justify-center gap-2.5">
        <TriangleAlert className="text-red-500" size={34} />
        <span className="text-xl font-medium leading-[150%] dark:text-white">
          Are you sure you want to delete this folder and its files?
        </span>
      </div>
      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button
          onClick={handleConfirmDelete}
          isLoading={isLoading}
          variant="primary"
        >
          Confirm
        </Button>
      </div>
    </WrapperModal>
  );
};
