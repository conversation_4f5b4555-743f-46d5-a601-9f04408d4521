import { useDraggable } from "@dnd-kit/core";
import { ColumnDef } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";

import {
  TableEditButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { File } from "@/lib/apis/isf-templates";

export const generateDocumentColumns = ({
  onEdit,
  onDelete,
  parentDirectoryName,
}: {
  onEdit: (file: File) => void;
  onDelete: (file: File) => void;
  parentDirectoryName: string | null;
}): ColumnDef<File>[] => [
  {
    id: "drag-handle",
    header: "",
    cell: function DragHandler({ row }) {
      const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
        id: `${row.original.title}`,
        data: {
          ...row.original,
          parentDirectoryName,
          type: "document",
        },
      });

      return (
        <div
          ref={setNodeRef}
          {...attributes}
          {...listeners}
          className="grid cursor-grab place-content-center"
          style={{ opacity: isDragging ? 0.5 : 1 }}
        >
          <GripVertical size={20} className="text-gray-500" />
        </div>
      );
    },
    enableSorting: false,
    meta: { width: "40px" },
  },
  {
    header: "Name",
    accessorKey: "title",
  },
  {
    header: "Description",
    accessorKey: "description",
  },
  {
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-2">
          <TableEditButton type="button" onClick={() => onEdit(row.original)} />
          <TableRemoveButton onClick={() => onDelete(row.original)} />
        </div>
      );
    },
  },
];
