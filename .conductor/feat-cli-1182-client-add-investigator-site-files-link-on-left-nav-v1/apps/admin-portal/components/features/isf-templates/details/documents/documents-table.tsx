import { useParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import React, { useMemo } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Table, TableLoading } from "@/components/ui/table";
import { File } from "@/lib/apis/isf-templates";

import { useDeleteFile } from "../hooks/use-isf-template-detail-mutations";
import { useIsfTemplateFiles } from "../hooks/use-isf-template-detail-queries";
import { generateDocumentColumns } from "./columns";

type Props = {
  onOpenEditDocument: (file: File) => void;
  draggingFile: File | null;
  isMovingDocument: boolean;
};

export type FileWithParentDirectoryName = File & {
  parentDirectoryName: string;
};

export const DocumentsTable = ({
  onOpenEditDocument,
  draggingFile,
  isMovingDocument,
}: Props) => {
  const [folderName] = useQueryState(
    "folderName",
    parseAsString.withDefault(""),
  );
  const params = useParams();
  const id = params.id as string;

  const { data, isPending, isPlaceholderData } = useIsfTemplateFiles(id);
  const { mutateAsync: deleteDocument, isPending: isDeleting } =
    useDeleteFile(id);

  const columns = useMemo(
    () =>
      generateDocumentColumns({
        parentDirectoryName: data?.name ?? null,
        onEdit: onOpenEditDocument,
        onDelete: (document) => {
          deleteDocument({
            title: document.title,
            parentDirectoryName: folderName,
          });
        },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [onOpenEditDocument, deleteDocument],
  );

  return (
    <>
      <div className="overflow-hidden rounded-lg border border-gray-300 dark:border-gray-500">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper
            isLoading={isMovingDocument || isPlaceholderData || isDeleting}
          >
            <Table
              columns={columns}
              data={data?.files ?? []}
              draggingRowId={draggingFile?.title || ""}
            />
          </LoadingWrapper>
        )}
      </div>
    </>
  );
};
