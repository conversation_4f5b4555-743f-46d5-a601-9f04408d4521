import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { IsfTemplate } from "@/lib/apis/isf-templates";

import {
  useCreateTemplate,
  useUpdateTemplate,
} from "./hooks/use-isf-template-mutations";

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  isActive: z.boolean().default(true),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  template: IsfTemplate | null;
};

export const TemplateModal = function ({ isOpen, onClose, template }: Props) {
  const { mutateAsync: createTemplate, isPending: isCreating } =
    useCreateTemplate();
  const { mutateAsync: updateTemplate, isPending: isUpdating } =
    useUpdateTemplate(template?.id);
  const isEditing = !!template;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    if (isEditing) {
      await updateTemplate({
        id: template.id,
        name: data.name,
        isActive: data.isActive,
      });
    } else {
      await createTemplate({ name: data.name });
    }
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? "Edit Template" : "Create Template"}
    >
      <Form
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={{
          name: template?.name || "",
          isActive:
            typeof template?.isActive === "boolean" ? template.isActive : true,
        }}
        className="space-y-4"
      >
        <div className="flex flex-col gap-2">
          <Label htmlFor="name">Name</Label>
          <InputField id="name" name="name" placeholder="Enter name..." />
        </div>

        {isEditing && (
          <div className="flex items-center gap-2">
            <Label htmlFor="isActive">Active</Label>
            <Checkbox id="isActive" name="isActive" />
          </div>
        )}

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            isLoading={isCreating || isUpdating}
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
