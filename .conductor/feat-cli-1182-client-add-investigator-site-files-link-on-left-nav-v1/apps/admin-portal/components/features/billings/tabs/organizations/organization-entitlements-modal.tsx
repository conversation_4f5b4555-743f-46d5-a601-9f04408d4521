"use client";

import { theme } from "flowbite-react";
import { useState } from "react";
import { twMerge } from "tailwind-merge";

import { useEntitlements } from "@/components/features/billings/tabs/entitlements/hooks/use-entitlements-queries";
import { useGroup } from "@/components/features/settings/groups/hooks/use-group";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { Group } from "@/lib/apis/groups/types";

import { generateEntitlementsColumns } from "./columns";
import { useUpdateEntitlementGroup } from "./hooks/use-organizations-mutations";

type OrganizationEntitlementsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  organization: Group;
};

export const OrganizationEntitlementsModal = ({
  isOpen,
  onClose,
  organization,
}: OrganizationEntitlementsModalProps) => {
  const [page, setPage] = useState(1);

  const {
    data: entitlementsData,
    isPending: isEntitlementsLoading,
    isPlaceholderData,
  } = useEntitlements({ page });
  const { data: groupData, isPending: isGroupLoading } = useGroup(
    organization?.id,
  );

  const { mutate } = useUpdateEntitlementGroup(organization.id);

  const columns = generateEntitlementsColumns({
    organizationEntitlements: groupData?.entitlements ?? [],
    onToggle: ({ entitlements, isAdding }) => {
      mutate({
        entitlements,
        isAdding,
      });
    },
  });

  return (
    <WrapperModal
      size="5xl"
      isOpen={isOpen}
      onClose={onClose}
      title={`Entitlements - ${organization.name}`}
      theme={{
        body: {
          base: twMerge(theme.modal.content.base, "p-0"),
        },
      }}
    >
      {isEntitlementsLoading || isGroupLoading ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table columns={columns} data={entitlementsData?.results ?? []} />
          {entitlementsData?.metadata && (
            <TableDataPagination
              metadata={entitlementsData.metadata}
              isUseExternalState
              page={page}
              setPage={setPage}
            />
          )}
        </LoadingWrapper>
      )}
    </WrapperModal>
  );
};
