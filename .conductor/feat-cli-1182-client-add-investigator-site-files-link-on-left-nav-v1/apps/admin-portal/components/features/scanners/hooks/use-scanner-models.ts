import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";

export const USE_SCANNER_MODELS_QUERY_KEY = "scannerModels";

export const useScannerModels = () => {
  const { page, take } = usePagination();

  return useQuery({
    queryKey: [USE_SCANNER_MODELS_QUERY_KEY, page, take],
    queryFn: () => api.scannerModels.list({ page, take }),
    placeholderData: (prevData) => prevData,
  });
};

export const useInfiniteScannerModels = (
  search: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-scanner-models", search],
    queryFn: ({ pageParam = 1 }) =>
      api.scannerModels.list({
        page: pageParam,
        take: initialPageSize,
        filter: {
          name: search,
        },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
