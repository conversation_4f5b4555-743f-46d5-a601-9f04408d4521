import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const USE_SCANNERS_QUERY_KEY = "scanners";

export const useScanners = (params?: MetadataParams) => {
  const { page, take } = usePagination();
  const { search } = useSearch();
  const searchParams = useSearchParams();
  const nonActive = searchParams.get("non-active") === "true";

  return useQuery({
    queryKey: [USE_SCANNERS_QUERY_KEY, page, take, search],
    queryFn: () =>
      api.scanners.list({
        page,
        take,
        ...params,
        filter: { displayName: search, isActive: nonActive ? undefined : true },
      }),
    placeholderData: (prevData) => prevData,
  });
};
