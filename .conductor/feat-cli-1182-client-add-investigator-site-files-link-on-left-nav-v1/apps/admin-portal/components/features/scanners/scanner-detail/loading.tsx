import { Card } from "flowbite-react";

import { Skeleton } from "@/components/ui/skeleton";

export const SiteDetailPageSkeleton = () => {
  return (
    <div className="flex flex-col gap-4 ">
      <div className="flex justify-between">
        <div className="flex items-center gap-4">
          {/* Back button skeleton */}
          <Skeleton className="h-9 w-9 rounded-full" />
          {/* Site name skeleton */}
          <Skeleton className="h-7 w-48" />
        </div>
        {/* Edit button skeleton */}
        <Skeleton className="h-9 w-28" />
      </div>

      <div>
        {/* Overview card skeleton */}
        <Card className="[&>div]:p-6">
          {/* Card title skeleton */}
          <Skeleton className="mb-6 h-6 w-24" />

          {/* Overview items grid */}
          <div className="grid grid-cols-2 gap-4">
            {/* Name */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-5 w-40" />
            </div>
            {/* Address */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-64" />
            </div>
            {/* Active Status */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-5 w-20" />
            </div>
            {/* Created Date */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-28" />
              <Skeleton className="h-5 w-32" />
            </div>
          </div>
        </Card>

        {/* Tabs skeleton */}
        <div className="mt-4"></div>
      </div>
    </div>
  );
};
