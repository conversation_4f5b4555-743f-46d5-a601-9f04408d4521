import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import type { Scanner } from "@/lib/apis/scanners/types";

import { useSites } from "../../sites/hooks/use-sites";
import { useAddScanner } from "../hooks/use-create-scanner";
import { useEditScanner } from "../hooks/use-edit-scanner";
import { useInfiniteScannerApplications } from "../hooks/use-scanner-applications";
import {
  useInfiniteScannerModels,
  useScannerModels,
} from "../hooks/use-scanner-models";

// Validation Schema
export const scannerSchema = z.object({
  displayName: z
    .string({ required_error: "Display Name is required" })
    .min(1, "Display Name is required"),
  description: z.string().optional(),
  currentVersionId: z
    .string({ required_error: "Current Version is required" })
    .uuid("Please select a valid Version ID"),
  targetVersionId: z
    .string({ required_error: "Target Version is required" })
    .uuid("Please select a valid Version ID"),
  modelId: z
    .string({ required_error: "Model is required" })
    .uuid("Please select a valid Model ID"),
  rebootFrequency: z.coerce
    .string({ required_error: "Reboot frequency is required" })
    .regex(/^\d+$/, "Reboot frequency must be greater or equal to 0")
    .transform((val) => parseInt(val, 10))
    .pipe(
      z
        .number()
        .int("Enter a whole number (e.g., 1, 2, 3)")
        .min(0, "Reboot frequency must be positive"),
    ),
  rebootHour: z
    .string({ required_error: "Reboot hour is required" })
    .regex(/^\d+$/, "Reboot hour must be between 0 and 23")
    .transform((val) => parseInt(val, 10))
    .pipe(
      z
        .number()
        .int("Enter a whole number (e.g., 1, 2, 3)")
        .min(0, "Reboot hour must be greater or equal to 0")
        .max(23, "Hour must be between 0 and 23"),
    ),
  rebootMinute: z
    .string({ required_error: "Reboot minute is required" })
    .regex(/^\d+$/, "Reboot minute must be between 0 and 59")
    .transform((val) => parseInt(val, 10))
    .pipe(
      z
        .number()
        .int("Enter a whole number (e.g., 1, 2, 3)")
        .min(0, "Reboot minute must be greater or equal to 0")
        .max(59, "Minute must be between 0 and 59"),
    ),
  updateFrequency: z
    .string()
    .regex(/^\d+$/, "Update frequency must be greater or equal to 0")
    .transform((val) => parseInt(val, 10))
    .pipe(
      z
        .number()
        .int("Enter a whole number (e.g., 1, 2, 3)")
        .min(0, "Update frequency must be greater or equal to 0"),
    )
    .optional(),
  isActive: z.coerce.boolean().optional(),
});

type ModalAddScannerProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddScanner = ({ isOpen, onClose }: ModalAddScannerProps) => {
  const { mutateAsync: addScanner, isPending: isAdding } = useAddScanner();
  const { data: sites } = useSites();
  const { data: models } = useScannerModels();

  async function onSubmit(data: z.infer<typeof scannerSchema>) {
    await addScanner(data);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Scanner</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={scannerSchema}
          onSubmit={(data) => onSubmit({ ...data, isActive: undefined })}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            displayName: "",
            description: "",
            currentVersionId: "",
            targetVersionId: "",
            modelId: "",
            rebootFrequency: 0,
            rebootHour: 0,
            rebootMinute: 0,
            updateFrequency: 0,
            isActive: true,
          }}
        >
          <ScannerForm
            onClose={onClose}
            isSubmitting={isAdding}
            sites={sites?.results ?? []}
            models={
              models?.results.map((model) => ({
                id: model.id,
                name: model.modelName,
              })) ?? []
            }
            isEditing={false}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type ModalEditScannerProps = {
  isOpen: boolean;
  onClose: () => void;
  scanner?: Scanner;
};

export const ModalEditScanner = ({
  isOpen,
  onClose,
  scanner,
}: ModalEditScannerProps) => {
  const { mutateAsync: editScanner, isPending: isEditing } = useEditScanner();
  const { data: sites } = useSites();
  const { data: models } = useScannerModels();

  async function onSubmit(data: z.infer<typeof scannerSchema>) {
    await editScanner({ id: scanner?.id || "", ...data });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Edit Scanner</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={scannerSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            displayName: scanner?.displayName || "",
            description: scanner?.description || "",
            currentVersionId: scanner?.currentVersion?.id || "",
            targetVersionId: scanner?.targetVersion?.id || "",
            modelId: scanner?.model?.id || "",
            rebootFrequency: scanner?.rebootFrequency || 0,
            rebootHour: scanner?.rebootHour || 0,
            rebootMinute: scanner?.rebootMinute || 0,
            updateFrequency: scanner?.updateFrequency || 0,
            isActive: scanner?.isActive,
          }}
        >
          <ScannerForm
            onClose={onClose}
            isSubmitting={isEditing}
            sites={sites?.results ?? []}
            models={
              models?.results.map((model) => ({
                id: model.id,
                name: model.modelName,
              })) ?? []
            }
            isEditing={true}
            scanner={scanner}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type ScannerFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
  sites: { id: string; name: string }[];
  models: { id: string; name: string }[];
  isEditing?: boolean;
  scanner?: Scanner;
};

const ScannerForm = ({
  onClose,
  isSubmitting,
  isEditing = false,
  scanner,
}: ScannerFormProps) => {
  return (
    <>
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6">
        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="displayName">Display Name</Label>
          <InputField
            id="displayName"
            name="displayName"
            placeholder="Enter display name..."
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="description">Description</Label>
          <InputField
            id="description"
            name="description"
            placeholder="Enter description..."
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="currentVersionId">Current Version</Label>
          <LazySelect
            name="currentVersionId"
            id="currentVersionId"
            placeholder="Select current version"
            useInfiniteQuery={useInfiniteScannerApplications}
            getOptionLabel={(option) => option.versionNumber}
            getOptionValue={(option) => option.id}
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="targetVersionId">Target Version</Label>
          <LazySelect
            name="targetVersionId"
            id="targetVersionId"
            placeholder="Select Target Version"
            useInfiniteQuery={useInfiniteScannerApplications}
            getOptionLabel={(option) => option.versionNumber}
            getOptionValue={(option) => option.id}
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="modelId">Model</Label>
          <LazySelect
            name="modelId"
            id="modelId"
            useInfiniteQuery={useInfiniteScannerModels}
            getOptionLabel={(model) => model.modelName}
            getOptionValue={(model) => model.id}
            placeholder="Select Model"
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="rebootFrequency">Reboot Frequency</Label>
          <InputField
            id="rebootFrequency"
            name="rebootFrequency"
            type="number"
            placeholder="Enter frequency..."
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="rebootHour">Reboot Hour</Label>
          <InputField
            id="rebootHour"
            name="rebootHour"
            type="number"
            placeholder="Enter reboot hour..."
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="rebootMinute">Reboot Minute</Label>
          <InputField
            id="rebootMinute"
            name="rebootMinute"
            type="number"
            placeholder="Enter reboot minute..."
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="updateFrequency">Update Frequency</Label>
          <InputField
            id="updateFrequency"
            name="updateFrequency"
            type="number"
            placeholder="Enter frequency..."
          />
        </div>

        {isEditing && (
          <div className="col-span-1 flex items-center gap-x-3">
            <Label htmlFor="isActive">Active Status</Label>
            <Checkbox
              id="isActive"
              name="isActive"
              className="h-5 w-5 text-blue-600 focus:ring-0"
            />
          </div>
        )}
      </div>
      <div className="mt-4 flex flex-col justify-end gap-5 border-none pt-0 sm:mt-6 sm:flex-row">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
