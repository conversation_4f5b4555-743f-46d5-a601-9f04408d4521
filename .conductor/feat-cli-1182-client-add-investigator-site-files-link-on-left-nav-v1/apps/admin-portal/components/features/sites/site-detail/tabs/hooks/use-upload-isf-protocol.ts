import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";

export const useUploadIsfProtocols = () => {
  return useMutation({
    mutationFn: (payload: {
      file: Blob;
      siteId: string | null;
      studyId: string | null;
      name: string;
    }) => {
      console.log(payload);
      if (!payload.siteId || !payload.studyId) throw Error;
      return api.IsfTemplateProtocolApi.uploadFromJson(
        payload.siteId,
        payload.studyId,
        payload.file,
        payload.name,
      );
    },
    onSuccess: () => {
      toast.success("ISF Protocol uploaded successfully!");
    },
    onError: (error) => {
      toast.error(
        error.message || "An error occurred while uploading the ISF Protocol",
      );
    },
  });
};
