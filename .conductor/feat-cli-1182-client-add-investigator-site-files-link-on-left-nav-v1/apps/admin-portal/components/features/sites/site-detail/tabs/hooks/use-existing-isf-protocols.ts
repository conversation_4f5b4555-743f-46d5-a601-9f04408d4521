import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";

// import { USE_SITE_CONTACTS_QUERY_KEY } from "./use-site-contacts";

export const useExistingIsfProtocol = () => {
  return useMutation({
    mutationFn: (payload: {
      id: string;
      siteId: string | null;
      studyId: string | null;
    }) => {
      if (!payload.siteId || !payload.studyId) {
        throw Error;
      }
      return api.IsfTemplateProtocolApi.buildFromExisting(
        payload.id,
        payload.siteId,
        payload.studyId,
      );
    },
    onSuccess: () => {
      toast.success("ISF Protocol built successfully");
    },
    onError: (error) => {
      toast.error(
        error.message || "An error occurred while building the ISF Protocol",
      );
    },
  });
};
