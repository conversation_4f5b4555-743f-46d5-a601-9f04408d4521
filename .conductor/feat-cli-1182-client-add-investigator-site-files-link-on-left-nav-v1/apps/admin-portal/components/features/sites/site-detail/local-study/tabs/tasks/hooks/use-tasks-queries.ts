import { skipToken, useQuery } from "@tanstack/react-query";
import { endOfDay, parse, startOfDay } from "date-fns";

import { TaskStatus } from "@/components/ui/badges/task-status-badge";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";
import { TAKE_ALL } from "@/lib/constants";

import { useTaskFilter } from "./use-task-filter";

export const localStudyTaskKeys = {
  all: () => ["local-study-tasks"] as const,

  allList: (studyId: string) => [...localStudyTaskKeys.all(), studyId] as const,
  list: (studyId: string, params?: MetadataParams) =>
    [...localStudyTaskKeys.allList(studyId), params] as const,
  allDetail: () => [...localStudyTaskKeys.all(), "detail"] as const,
  detail: (taskId: string) =>
    [...localStudyTaskKeys.allDetail(), taskId] as const,
  taskActivities: (taskId: string) =>
    [...localStudyTaskKeys.detail(taskId), "activities"] as const,
};

export const useStudyTasks = (studyId: string) => {
  const {
    page,
    take,
    search,
    priority,
    status,
    orderBy,
    orderDirection,
    from,
    to,
  } = useTaskFilter();

  const fromDueDate = from
    ? startOfDay(parse(from, "M/d/yyyy", new Date())).toISOString()
    : undefined;
  const toDueDate = to
    ? endOfDay(parse(to, "M/d/yyyy", new Date())).toISOString()
    : undefined;

  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      name: search,
      status: status || undefined,
      priority: priority || undefined,
      fromDueDate,
      toDueDate,
    },
  };
  return useQuery({
    queryKey: localStudyTaskKeys.list(studyId, params),
    queryFn: () => api.tasks.getTasksByStudyId(studyId, params),
    placeholderData: (prev) => prev,
  });
};

export const useStudyTask = ({
  taskId,
  enabled,
}: {
  taskId?: string;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: enabled && taskId ? localStudyTaskKeys.detail(taskId) : [],
    queryFn: enabled && taskId ? () => api.tasks.getTask(taskId) : skipToken,
  });
};

export const useTaskActivities = ({
  taskId,
  enabled,
}: {
  taskId?: string;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey:
      enabled && taskId ? localStudyTaskKeys.taskActivities(taskId) : [],
    queryFn:
      enabled && taskId ? () => api.tasks.recentActivities(taskId) : skipToken,
  });
};

export const useStudyTasksByStatus = ({
  studyId,
  status,
}: {
  studyId: string;
  status: TaskStatus;
}) => {
  const { search, priority, orderBy, orderDirection, from, to } =
    useTaskFilter();

  const fromDueDate = from
    ? startOfDay(parse(from, "M/d/yyyy", new Date())).toISOString()
    : undefined;
  const toDueDate = to
    ? endOfDay(parse(to, "M/d/yyyy", new Date())).toISOString()
    : undefined;

  const params = {
    take: TAKE_ALL,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      name: search,
      status: status,
      priority: priority || undefined,
      fromDueDate,
      toDueDate,
    },
  };
  return useQuery({
    queryKey: localStudyTaskKeys.list(studyId, params),
    queryFn: () => api.tasks.getTasksByStudyId(studyId, params),
    placeholderData: (prev) => prev,
  });
};
