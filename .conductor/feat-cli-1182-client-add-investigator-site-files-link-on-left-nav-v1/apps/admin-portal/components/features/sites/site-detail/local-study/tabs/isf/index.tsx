"use client";
import {
  <PERSON>nd<PERSON><PERSON><PERSON><PERSON>,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { Card, Tooltip } from "flowbite-react";
import React, { useState } from "react";
import { CiCirclePlus, CiImport } from "react-icons/ci";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";

import { useLocalStudy } from "@/components/features/sites/site-detail/local-study/hooks/use-local-study-queries";
import { SearchField } from "@/components/shared";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { ROLLBACK_ACTIONS, useActionHistory } from "@/hooks/use-action-history";
import { useDisclosure } from "@/hooks/use-disclosure";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Document } from "@/lib/apis/essential-document-files/types";
import { Folder } from "@/lib/apis/isf-folders";

import { DocumentsTable } from "./documents/documents-table";
import { FileOverlay } from "./documents/file-overlay";
import { UploadDocumentModal } from "./documents/upload-document-modal";
import { FilterEBinder } from "./filter-ebinder";
import { FolderWithPath } from "./folders/folder-node";
import { FolderOverlay } from "./folders/folder-overlay";
import { FolderColumn } from "./folders/folders-column";
import { useFilterDocuments } from "./hooks/use-filter-documents";
import {
  useCreateSystemFolder,
  useMoveDocument,
  useMoveFolder,
  useScaffoldFolders,
} from "./hooks/use-isf-mutations";
import { useDocuments, useFolders } from "./hooks/use-isf-queries";
import { PlaceholderModal } from "./placeholder-modal";
import { ImportModal } from "./template-modal";

export const IsfTab = () => {
  const {
    siteId,
    studyId,
    isShowPlaceholder,
    setIsShowPlaceholder,
    showAllDetails,
    setShowAllDetails,
    folderId,
  } = useFilterDocuments();
  const { addAction } = useActionHistory({
    onRollBack: async (lastAction) => {
      if (lastAction.type === ROLLBACK_ACTIONS.moveFolder) {
        await moveFolder({
          folderId: lastAction.folderId,
          parentDirectory: lastAction.oldFolderId,
          siteId,
          studyId,
          isRollback: true,
        });
      }

      if (lastAction.type === ROLLBACK_ACTIONS.moveDocument) {
        await moveDocument({
          parentDirectoryId: lastAction.oldFolderId,
          siteId,
          studyId,
          isRollback: true,
          id: lastAction.documentId,
        });
      }
    },
    isAllowKeyboard: true,
  });

  // const { data: site, isPending: isPendingSite } = useSite(siteId);
  // const { data: study, isPending: isPendingStudy } = useStudy(studyId);
  const { data: localStudy, isPending: isPendingLocalStudy } = useLocalStudy({
    siteId,
    studyId,
  });
  const { mutateAsync: scaffold, isPending: isScaffolding } =
    useScaffoldFolders();

  const { mutateAsync: createSystemFolder, isPending: isCreatingSystemFolder } =
    useCreateSystemFolder();

  const { data: folders, isPending: isPendingFolders } = useFolders(
    studyId,
    siteId,
  );
  const { data: documents, isPending: isPendingDocuments } = useDocuments();

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 10, // Require the mouse to move by 10 pixels before activating
    },
  });
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 250, // Press delay of 250ms, with tolerance of 5px of movement
      tolerance: 5,
    },
  });

  const sensors = useSensors(mouseSensor, touchSensor);

  const isTablet = useMediaQuery("(min-width: 768px)");

  // const {
  //   isOpen: isExportOpen,
  //   open: onExportOpen,
  //   close: onExportClose,
  // } = useDisclosure();

  const {
    isOpen: isImportOpen,
    open: onImportOpen,
    close: onImportClose,
  } = useDisclosure();

  const {
    isOpen: isPlaceholderOpen,
    open: onPlaceholderOpen,
    close: onPlaceholderClose,
  } = useDisclosure();

  const {
    isOpen: isUploadOpen,
    open: onUploadOpen,
    close: onUploadClose,
  } = useDisclosure();

  const handleScaffoldFolders = () => {
    scaffold({ siteId, studyId });
  };

  const [draggingFolder, setDraggingFolder] = useState<Folder | null>(null);
  const [draggingFile, setDraggingFile] = useState<Document | null>(null);
  const [selectedFile, setSelectedFile] = useState<Document | null>(null);
  const [activePath, setActivePath] = useState("");

  const { mutateAsync: moveFolder, isPending: isMovingFolder } =
    useMoveFolder();

  const { mutateAsync: moveDocument, isPending: isMovingDocument } =
    useMoveDocument();

  const isDisabledImportTemplate =
    isPendingDocuments ||
    isPendingFolders ||
    !!folders?.length ||
    !!documents?.metadata.totalCount;

  const handleClosePlaceholderModal = () => {
    onPlaceholderClose();
    setSelectedFile(null);
  };

  const handleOpenUpdatePlaceholderModal = (file: Document) => {
    onPlaceholderOpen();
    setSelectedFile(file);
  };

  const handleDragStart = (e: DragStartEvent) => {
    const unknownDraggingItem = e.active.data.current;

    if (unknownDraggingItem?.type === "document") {
      return setDraggingFile(unknownDraggingItem as Document);
    }
    setDraggingFolder(unknownDraggingItem as Folder);
  };
  const handleDragEnd = async (e: DragEndEvent) => {
    const source = e.active.data.current;
    const target = e.over?.data.current as FolderWithPath | undefined;

    setDraggingFolder(null);
    setDraggingFile(null);

    // handle move document
    if (source?.type === "document") {
      const file = source as Document;
      if (file?.isfFolderId === target?.id || !target?.id) return;

      await moveDocument({
        parentDirectoryId: target.id,
        siteId,
        studyId,
        id: file.id,
      });
      addAction({
        documentId: file.id,
        type: ROLLBACK_ACTIONS.moveDocument,
        newFolderId: target.id,
        oldFolderId: file.isfFolderId as string,
      });
      return;
    }

    // handle move folder
    const folder = source as FolderWithPath;

    if (
      (!folder.parentDirectoryId && !target?.id) || // Dragging a a top-level to the root
      folder.id === target?.id || // Dragging a folder onto itself
      folder.parentDirectoryId === target?.id || // Dragging a folder to its current parent
      target?.path?.startsWith(folder.path) // Dragging a parent folder into its own subfolder
    )
      return;

    await moveFolder({
      folderId: folder.id,
      parentDirectory: target?.id || null,
      siteId,
      studyId,
    });
    addAction({
      type: ROLLBACK_ACTIONS.moveFolder,
      oldFolderId: folder.parentDirectoryId,
      newFolderId: target?.id || null,
      folderId: folder.id,
    });
  };

  const handleCreateSystemFolder = () => {
    createSystemFolder({ siteId, studyId });
  };

  return (
    <>
      <Card className="[&>div]:p-2 sm:[&>div]:p-4">
        <div className="mb-4 flex flex-col justify-between gap-4 lg:flex-row lg:items-center">
          <h2 className="text-2xl font-semibold dark:text-white">
            Investigator Site File
          </h2>
          <div className="flex justify-end gap-2.5">
            {isTablet && (
              <div>
                <SearchField placeholder="Search all documents..." />
              </div>
            )}
            <FilterEBinder />
            {isTablet && (
              <>
                <div className="ml-2.5 flex items-center gap-2">
                  <div className="text-[10px] font-medium dark:text-white">
                    Show <br /> Placeholder
                  </div>
                  <Switch
                    checked={isShowPlaceholder || false}
                    onChange={(checked) =>
                      setIsShowPlaceholder(checked ? true : null)
                    }
                  />
                </div>
                <div className="ml-2.5 flex items-center gap-2">
                  <div className="text-[10px] font-medium dark:text-white">
                    Show <br /> All Details
                  </div>
                  <Switch
                    checked={showAllDetails}
                    onChange={(checked) =>
                      setShowAllDetails(checked ? true : null)
                    }
                  />
                </div>
              </>
            )}
          </div>
        </div>
        <div className="flex flex-1 flex-col gap-4 ">
          <div className="grid gap-2 sm:grid-cols-2 xl:ml-auto xl:w-fit xl:grid-flow-col xl:grid-cols-1">
            <Button
              className="w-full xl:w-fit"
              onClick={handleCreateSystemFolder}
              variant="primary"
              isLoading={isCreatingSystemFolder}
            >
              <CiCirclePlus size={18} /> Create System Folders
            </Button>

            {isDisabledImportTemplate ? (
              <Tooltip
                theme={{
                  content: "w-full xl:w-fit",
                  target: "w-full xl:w-fit",
                }}
                content="Folders already existed"
              >
                <Button
                  className="w-full xl:w-fit"
                  disabled={isDisabledImportTemplate}
                  onClick={onImportOpen}
                  variant="primary"
                >
                  <CiImport size={18} /> Import Template
                </Button>
              </Tooltip>
            ) : (
              <Button
                className="w-full xl:w-fit"
                disabled={isDisabledImportTemplate}
                onClick={onImportOpen}
                variant="primary"
              >
                <CiImport size={18} /> Import Template
              </Button>
            )}

            {localStudy?.isScaffolded ? (
              <Tooltip
                theme={{
                  content: "w-full xl:w-fit",
                  target: "w-full xl:w-fit",
                }}
                content="Folders already created"
              >
                <Button
                  className="w-full xl:w-fit"
                  disabled={true}
                  variant="primary"
                >
                  <CiCirclePlus size={18} /> Scaffold Folders
                </Button>
              </Tooltip>
            ) : (
              <Button
                className="w-full xl:w-fit"
                isLoading={isScaffolding}
                disabled={isPendingLocalStudy}
                onClick={handleScaffoldFolders}
                variant="primary"
              >
                <CiCirclePlus size={18} /> Scaffold Folders
              </Button>
            )}

            {!folderId ? (
              <Tooltip
                theme={{
                  content: "w-full xl:w-fit",
                  target: "w-full xl:w-fit",
                }}
                content="Select a folder to upload document"
              >
                <Button
                  className="w-full xl:w-fit"
                  disabled={!folderId}
                  variant="primary"
                >
                  <CiCirclePlus size={18} /> Upload Document
                </Button>
              </Tooltip>
            ) : (
              <Button
                className="w-full xl:w-fit"
                onClick={onUploadOpen}
                variant="primary"
              >
                <CiCirclePlus size={18} /> Upload Document
              </Button>
            )}
          </div>
          <div className="flex flex-1 flex-col gap-4">
            <DndContext
              sensors={sensors}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              modifiers={[restrictToWindowEdges]}
            >
              {isTablet ? (
                <PanelGroup direction="horizontal">
                  <Panel defaultSize={20} minSize={20}>
                    <FolderColumn
                      draggingFolder={draggingFolder}
                      draggingFile={draggingFile}
                      isMovingFolder={isMovingFolder}
                      isMovingDocument={isMovingDocument}
                      activePath={activePath}
                      setActivePath={setActivePath}
                    />
                  </Panel>
                  <PanelResizeHandle className="mx-[5px] hidden w-1 hover:bg-blue-400 active:bg-blue-500 sm:block" />
                  <Panel minSize={20}>
                    <DocumentsTable
                      isMovingDocument={isMovingDocument}
                      onOpenEditDocument={handleOpenUpdatePlaceholderModal}
                      draggingFile={draggingFile}
                    />
                  </Panel>
                </PanelGroup>
              ) : (
                <div className="space-y-4">
                  <FolderColumn
                    draggingFolder={draggingFolder}
                    draggingFile={draggingFile}
                    isMovingFolder={isMovingFolder}
                    isMovingDocument={isMovingDocument}
                    activePath={activePath}
                    setActivePath={setActivePath}
                  />
                  <DocumentsTable
                    isMovingDocument={isMovingDocument}
                    onOpenEditDocument={handleOpenUpdatePlaceholderModal}
                    draggingFile={draggingFile}
                  />
                </div>
              )}

              <DragOverlay
                adjustScale={false}
                zIndex={9999}
                dropAnimation={null}
              >
                {!!draggingFile && <FileOverlay document={draggingFile} />}

                {!!draggingFolder && (
                  <FolderOverlay
                    folderName={draggingFolder?.name}
                    fileCount={draggingFolder?.fileCount}
                  />
                )}
              </DragOverlay>
            </DndContext>
          </div>
        </div>
      </Card>
      {/* <ExportModal isOpen={isExportOpen} onClose={onExportClose} /> */}
      <ImportModal isOpen={isImportOpen} onClose={onImportClose} />

      {isPlaceholderOpen && (
        <PlaceholderModal
          selectedFile={selectedFile}
          isOpen={isPlaceholderOpen}
          onClose={handleClosePlaceholderModal}
        />
      )}

      {isUploadOpen && (
        <UploadDocumentModal
          onCreatePlaceHolder={() => {
            onUploadClose();
            onPlaceholderOpen();
          }}
          isOpen={isUploadOpen}
          onClose={onUploadClose}
          path={activePath}
        />
      )}
    </>
  );
};
