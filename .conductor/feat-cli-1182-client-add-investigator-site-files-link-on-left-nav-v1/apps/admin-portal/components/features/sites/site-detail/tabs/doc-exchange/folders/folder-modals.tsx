import { useParams } from "next/navigation";
import React from "react";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { DocExchangeFolder } from "@/lib/apis/doc-exchange/types";

import {
  useAddFolder,
  useUpdateFolder,
} from "../hooks/use-doc-exchange-mutations";

const exportSchema = z.object({
  name: z
    .string({
      required_error: "Folder name is required",
      invalid_type_error: "Folder name is required",
    })
    .min(1, "Folder name is required"),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const FolderModal = function ({
  isOpen,
  onClose,
  selectedFolder,
}: Props & {
  selectedFolder: DocExchangeFolder | null;
}) {
  const siteId = useParams()?.id as string;
  const { mutateAsync: addFolder, isPending: isAdding } = useAddFolder();
  const { mutateAsync: updateFolder, isPending: isEditing } =
    useUpdateFolder(siteId);

  const onSubmit = async (data: z.infer<typeof exportSchema>) => {
    !selectedFolder
      ? await addFolder({ ...data, siteId })
      : await updateFolder({
          ...data,
          folderId: selectedFolder.id,
        });
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      title={!selectedFolder ? "New Folder" : "Update Folder"}
    >
      <Form
        mode="onChange"
        schema={exportSchema}
        onSubmit={onSubmit}
        defaultValues={{
          name: selectedFolder?.name || "",
        }}
      >
        <div className="flex flex-col gap-2">
          <Label htmlFor="name">Folder Name</Label>
          <InputField
            id="name"
            name="name"
            placeholder="Enter folder name..."
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            isLoading={isAdding || isEditing}
            type="submit"
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
