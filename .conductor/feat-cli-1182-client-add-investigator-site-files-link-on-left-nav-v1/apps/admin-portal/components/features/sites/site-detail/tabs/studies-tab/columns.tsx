import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { StudyStatusBadge } from "@/components/ui/badges";
import type { SiteStudy } from "@/lib/apis/sites";

export const generateColumns = (
  onBuildIsf: (study: SiteStudy) => void,
): ColumnDef<SiteStudy>[] => {
  return [
    {
      header: "Local Study",
      accessorKey: "study.name",
      cell: ({ row }) => {
        return (
          <Link
            className=" text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
            href={`/sites/${row.original.siteId}/studies/${row.original.studyId}`}
          >
            {row.original.study.name}
          </Link>
        );
      },
    },
    {
      header: "Parent Study",
      accessorKey: "study.name",
      cell: ({ row }) => {
        return (
          <Link
            className=" text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
            href={`/studies/${row.original.studyId}`}
          >
            {row.original.study.name}
          </Link>
        );
      },
    },
    {
      header: "NCT",
      accessorKey: "study.nct",
    },
    {
      header: "Study Code",
      accessorKey: "study.studyCode",
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => {
        return <StudyStatusBadge status={row.original.status} />;
      },
    },
    {
      header: "Patient Target",
      accessorKey: "patientTarget",
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: ({ row }) => {
        const study = row.original;
        return (
          <div className="leading-4.5 text-primary-500 flex gap-x-4 text-xs font-medium">
            <TableViewButton
              type="link"
              href={`/sites/${study.siteId}/studies/${study.studyId}`}
            />
          </div>
        );
      },
    },
  ];
};
