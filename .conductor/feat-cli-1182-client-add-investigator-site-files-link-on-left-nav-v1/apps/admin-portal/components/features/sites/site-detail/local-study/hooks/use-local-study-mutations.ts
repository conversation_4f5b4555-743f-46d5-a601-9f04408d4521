import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { LocalStudyParams, UpdateSiteStudyPayload } from "@/lib/apis/sites";

import { localStudyKeys } from "./use-local-study-queries";

export const useUpdateLocalStudy = ({ siteId, studyId }: LocalStudyParams) => {
  return useMutation({
    mutationFn: (
      payload: UpdateSiteStudyPayload & { id: string; studyId: string },
    ) => {
      const { id, studyId, ...rest } = payload;
      return api.sites.updateStudy(id, studyId, rest);
    },
    onSettled: (_, err) => {
      !err && toast.success("Study updated successfully");
    },
    onError: (error) => {
      toast.error(error?.message || "Fail to update study");
    },
    meta: {
      awaits: localStudyKeys.detail({
        siteId,
        studyId,
      }),
    },
  });
};
