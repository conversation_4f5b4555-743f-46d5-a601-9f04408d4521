import { useParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";

export const useFilterDocuments = () => {
  const siteId = useParams().id as string;
  const studyId = useParams().studyId as string;

  const { search, changeSearch } = useSearch();
  const [, setSearch] = useQueryState("search", parseAsString);

  const { page, take, goToPage } = usePagination();
  const { orderBy, orderDirection, changeSort } = useSort();
  const [folderId, setFolderId] = useQueryState("folderId", parseAsString);

  const [status, setStatus] = useQueryState("status", parseAsString);
  const [extension, setExtension] = useQueryState("extension", parseAsString);
  const [fromCreatedDate, setFromCreatedDate] = useQueryState(
    "fromCreatedDate",
    parseAsString,
  );
  const [toCreatedDate, setToCreatedDate] = useQueryState(
    "toCreatedDate",
    parseAsString,
  );

  return {
    siteId,
    search,
    page,
    take,
    orderBy,
    orderDirection,
    changeSort,
    folderId,
    setFolderId,
    status,
    setStatus,
    extension,
    setExtension,
    fromCreatedDate,
    setFromCreatedDate,
    toCreatedDate,
    setToCreatedDate,
    studyId: studyId as string,
    changeSearch,
    setSearch,
    goToPage,
  };
};
