import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";

export const useTaskFilter = () => {
  const [status, setStatus] = useQueryState("status", parseAsString);
  const [isTable, setIsTable] = useQueryState("isTable", parseAsBoolean);

  const [from, setFrom] = useQueryState("from", parseAsString);
  const [to, setTo] = useQueryState("to", parseAsString);
  const [priority, setPriority] = useQueryState("priority", parseAsString);
  const { orderBy, orderDirection, changeSort } = useSort();
  const { search } = useSearch();
  const { page, take } = usePagination();

  return {
    status,
    setStatus,
    from,
    setFrom,
    to,
    setTo,
    priority,
    setPriority,
    orderBy,
    orderDirection,
    changeSort,
    search,
    page,
    take,
    isTable,
    setIsTable,
  };
};
