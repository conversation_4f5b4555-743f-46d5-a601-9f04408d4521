import { useEffect, useRef, useState } from "react";
import { z } from "zod";

import { Button, CloseButton } from "@/components/ui/button";
import {
  Form,
  FormActions,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Document } from "@/lib/apis/essential-document-files/types";

import { useFilterDocuments } from "./hooks/use-filter-documents";
import {
  useCreatePlaceholder,
  useUpdateDocument,
} from "./hooks/use-isf-mutations";
import {
  useIsfArtifacts,
  useIsfSections,
  useIsfZones,
} from "./hooks/use-isf-queries";

const schema = z.object({
  title: z
    .string({
      invalid_type_error: "File name is required",
      required_error: "File name is required",
    })
    .min(1, { message: "File name is required" }),
  categoryId: z
    .string({
      invalid_type_error: "Artifact is required",
      required_error: "Artifact is required",
    })
    .min(1, { message: "Artifact is required" })
    .optional(),
  description: z.string().optional(),
  zone: z
    .string({
      invalid_type_error: "Zone is required",
      required_error: "Zone is required",
    })
    .min(1, { message: "Zone is required" })
    .optional(),
  section: z
    .string({
      invalid_type_error: "Section is required",
      required_error: "Section is required",
    })
    .min(1, { message: "Section is required" })
    .optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedFile: Document | null;
};

export const PlaceholderModal = function ({
  isOpen,
  onClose,
  selectedFile,
}: Props) {
  const { siteId, studyId, folderId } = useFilterDocuments();

  const [zone, setZone] = useState(selectedFile?.category?.zoneName || "");
  const [section, setSection] = useState(
    selectedFile?.category?.sectionName || "",
  );
  const [artifact, setArtifact] = useState("");
  const formRef = useRef<FormActions<z.ZodType<z.infer<typeof schema>>>>(null);

  const { data: zones } = useIsfZones();
  const { data: sections } = useIsfSections(zone);
  const { data: artifacts } = useIsfArtifacts(zone, section);
  const { mutateAsync: createPlaceholder, isPending: isCreating } =
    useCreatePlaceholder();

  const { mutateAsync: updatePlaceholder, isPending: isUpdating } =
    useUpdateDocument(selectedFile?.id);

  const isEditing = !!selectedFile;

  const handleZoneChange = (value: string) => {
    setZone(value);
  };

  const handleSectionChange = (value: string) => {
    setSection(value);
  };
  const handleArtifactChange = (value: string) => {
    setArtifact(value);
  };

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updatePlaceholder({
          ...data,
          id: selectedFile.id,
        })
      : await createPlaceholder({
          ...data,
          siteId,
          studyId,
          isfFolderId: folderId as string,
        });
    onClose();
  };

  useEffect(() => {
    if (artifact && artifacts) {
      const selectedArtifact = artifacts.find((a) => a.id === artifact);
      if (selectedArtifact?.subartifact) {
        formRef.current?.formHandler.setValue(
          "title",
          selectedArtifact.subartifact,
          {
            shouldValidate: true,
          },
        );
      }
    }
  }, [artifact, artifacts]);

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${selectedFile ? "Update" : "Create"} Document`}
    >
      <Form
        ref={formRef}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={{
          title: selectedFile?.title || "",
          categoryId: selectedFile?.categoryId || "",
          description: selectedFile?.description || "",
          zone: selectedFile?.category?.zoneName || undefined,
          section: selectedFile?.category?.sectionName || "",
        }}
        className="space-y-4"
      >
        <div className="grid gap-4 sm:grid-cols-2 sm:gap-2">
          <div className="flex flex-col gap-2">
            <Label htmlFor="title">Zone</Label>
            <Select
              id="zone"
              name="zone"
              placeholder="Select zone"
              options={zones?.reduce(
                (acc: { label: string; value: string }[], zone) =>
                  zone?.name
                    ? [
                        ...acc,
                        {
                          label: zone?.name,
                          value: zone?.name,
                        },
                      ]
                    : acc,
                [],
              )}
              onChange={handleZoneChange}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="title">Section</Label>
            <Select
              id="section"
              name="section"
              placeholder="Select section"
              options={sections?.map((section) => ({
                label: section.name,
                value: section.name,
              }))}
              onChange={handleSectionChange}
            />
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="title">Artifact Name</Label>
          <Select
            id="categoryId"
            name="categoryId"
            placeholder="Select artifact name"
            options={artifacts?.map((artifact) => ({
              label: artifact.name,
              value: artifact.id,
            }))}
            onChange={handleArtifactChange}
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="title">File Name</Label>
          <InputField
            id="title"
            name="title"
            placeholder="Enter file name..."
          />
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            placeholder="Enter description..."
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            disabled={isCreating || isUpdating}
            isLoading={isCreating || isUpdating}
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};

type ArtifactModalProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedFile: Document;
};

export const ArtifactModal = function ({
  isOpen,
  onClose,
  selectedFile,
}: ArtifactModalProps) {
  const [zone, setZone] = useState(selectedFile?.category?.zoneName || "");
  const [section, setSection] = useState(
    selectedFile?.category?.sectionName || "",
  );
  const [artifact, setArtifact] = useState("");
  const formRef = useRef<FormActions<z.ZodType<z.infer<typeof schema>>>>(null);

  const { data: zones } = useIsfZones();
  const { data: sections } = useIsfSections(zone);
  const { data: artifacts } = useIsfArtifacts(zone, section);

  const { mutateAsync: updatePlaceholder, isPending: isUpdating } =
    useUpdateDocument(selectedFile.id);

  const handleZoneChange = (value: string) => {
    setZone(value);
  };

  const handleSectionChange = (value: string) => {
    setSection(value);
  };
  const handleArtifactChange = (value: string) => {
    setArtifact(value);
  };

  const onSubmit = async (data: z.infer<typeof schema>) => {
    if (!selectedFile) return;
    await updatePlaceholder({
      ...data,
      id: selectedFile.id,
    });
    onClose();
  };

  useEffect(() => {
    if (artifact && artifacts) {
      const selectedArtifact = artifacts.find((a) => a.id === artifact);
      if (selectedArtifact?.subartifact) {
        formRef.current?.formHandler.setValue(
          "title",
          selectedArtifact.subartifact,
          {
            shouldValidate: true,
          },
        );
      }
    }
  }, [artifact, artifacts]);

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title={`Update Artifact`}>
      <Form
        ref={formRef}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={{
          categoryId: selectedFile?.categoryId || "",
          zone: selectedFile?.category?.zoneName || undefined,
          section: selectedFile?.category?.sectionName || "",
        }}
        className="space-y-4"
      >
        <div className="grid gap-4 sm:grid-cols-2 sm:gap-2">
          <div className="flex flex-col gap-2">
            <Label htmlFor="title">Zone</Label>
            <Select
              id="zone"
              name="zone"
              placeholder="Select zone"
              options={zones?.reduce(
                (acc: { label: string; value: string }[], zone) =>
                  zone?.name
                    ? [
                        ...acc,
                        {
                          label: zone?.name,
                          value: zone?.name,
                        },
                      ]
                    : acc,
                [],
              )}
              onChange={handleZoneChange}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="title">Section</Label>
            <Select
              id="section"
              name="section"
              placeholder="Select section"
              options={sections?.map((section) => ({
                label: section.name,
                value: section.name,
              }))}
              onChange={handleSectionChange}
            />
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="title">Artifact Name</Label>
          <Select
            id="categoryId"
            name="categoryId"
            placeholder="Select artifact name"
            options={artifacts?.map((artifact) => ({
              label: artifact.name,
              value: artifact.id,
            }))}
            onChange={handleArtifactChange}
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            disabled={isUpdating}
            isLoading={isUpdating}
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
