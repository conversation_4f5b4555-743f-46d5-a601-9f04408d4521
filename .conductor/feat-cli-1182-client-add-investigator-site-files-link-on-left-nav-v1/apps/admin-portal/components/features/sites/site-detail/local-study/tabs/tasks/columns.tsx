import { ColumnDef } from "@tanstack/react-table";

import {
  TableEditButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { TaskPriorityBadge } from "@/components/ui/badges/task-priority-badge";
import { TaskStatusBadge } from "@/components/ui/badges/task-status-badge";
import { Task } from "@/lib/apis/tasks";
import { formatDate } from "@/lib/utils";

export const generateTaskColumns = ({
  onEdit,
  onView,
  onDelete,
}: {
  onView: (taskId: string) => void;
  onEdit: (task: Task) => void;
  onDelete: (task: Task) => void;
}): ColumnDef<Task>[] => [
  {
    header: "Task",
    accessorKey: "name",
    cell: ({ row }) => {
      return (
        <button
          className=" text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
          // href={`/patients/${row.original.id}`}
          onClick={() => onView(row.original.id)}
        >
          {row.original.name}
        </button>
      );
    },
  },
  {
    header: "Assignee",
    accessorKey: "assignedTo.profile.user.fullName",
    id: "assignedToProfile.name",
  },
  {
    header: "Due Date",
    accessorKey: "dueDate",
    cell: ({ row }) => {
      return row.original.dueDate
        ? formatDate(row.original.dueDate, "LLL dd, yyyy")
        : "N/A";
    },
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => {
      return <TaskStatusBadge variant={row.original.status} />;
    },
  },
  {
    header: "Priority",
    accessorKey: "priority",
    cell: ({ row }) => {
      return <TaskPriorityBadge variant={row.original.priority} />;
    },
  },
  {
    header: "Action",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-4">
          <TableEditButton type="button" onClick={() => onEdit(row.original)} />
          <TableRemoveButton onClick={() => onDelete(row.original)} />
        </div>
      );
    },
  },
];
