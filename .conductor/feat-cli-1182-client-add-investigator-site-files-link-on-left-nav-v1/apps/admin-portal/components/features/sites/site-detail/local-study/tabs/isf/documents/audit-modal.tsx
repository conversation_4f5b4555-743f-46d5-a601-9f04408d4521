import { useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { useAuditLogs } from "../hooks/use-isf-queries";
import { auditLogColumns } from "./columns";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
};

const AuditModal = ({ isOpen, onClose, documentId }: Props) => {
  const [page, setPage] = useState(1);
  const { data, isPending, isPlaceholderData } = useAuditLogs({
    id: documentId,
    page,
  });
  return (
    <WrapperModal
      size="4xl"
      isOpen={isOpen}
      onClose={onClose}
      title="Audit Logs"
    >
      {isPending ? (
        <TableLoading columns={auditLogColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <div className="rounded-lg">
            <Table
              columns={auditLogColumns}
              data={data?.results ?? []}
              headCellStyle="text-gray-500 p-4 !rounded-none"
            />
          </div>
          {data?.metadata && (
            <TableDataPagination
              isUseExternalState
              page={page}
              setPage={setPage}
              metadata={data.metadata}
            />
          )}
        </LoadingWrapper>
      )}
    </WrapperModal>
  );
};

export default AuditModal;
