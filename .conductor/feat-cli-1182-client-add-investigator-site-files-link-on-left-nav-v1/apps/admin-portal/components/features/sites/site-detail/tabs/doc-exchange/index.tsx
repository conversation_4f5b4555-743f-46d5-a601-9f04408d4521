"use client";
import {
  <PERSON>nd<PERSON><PERSON><PERSON><PERSON>,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { Card, Tooltip } from "flowbite-react";
import React, { useState } from "react";
import { CiCirclePlus } from "react-icons/ci";

import { SearchField } from "@/components/shared";
import { Button } from "@/components/ui/button";
import { ROLLBACK_ACTIONS, useActionHistory } from "@/hooks/use-action-history";
import { useDisclosure } from "@/hooks/use-disclosure";
import {
  DocExchangeDocument,
  DocExchangeFolder,
} from "@/lib/apis/doc-exchange/types";

import { DocumentsTable } from "./documents/documents-table";
import { FileOverlay } from "./documents/file-overlay";
import { UploadDocumentModal } from "./documents/upload-document-modal";
import { FilterEBinder } from "./filter-doc-exchange";
import { FolderWithPath } from "./folders/folder-node";
import { FolderOverlay } from "./folders/folder-overlay";
import { FolderColumn } from "./folders/folders-column";
import {
  useMoveDocument,
  useMoveFolder,
} from "./hooks/use-doc-exchange-mutations";
import { useFilterDocuments } from "./hooks/use-filter-documents";

export const DocExchangeTab = () => {
  const { siteId, studyId, folderId } = useFilterDocuments();
  const { addAction } = useActionHistory({
    onRollBack: async (lastAction) => {
      if (lastAction.type === ROLLBACK_ACTIONS.moveFolder) {
        await moveFolder({
          folderId: lastAction.folderId,
          parentDirectory: lastAction.oldFolderId,
          siteId,
          studyId,
          isRollback: true,
        });
      }

      if (lastAction.type === ROLLBACK_ACTIONS.moveDocument) {
        await moveDocument({
          parentDirectoryId: lastAction.oldFolderId,
          isRollback: true,
          documentId: lastAction.documentId,
        });
      }
    },
    isAllowKeyboard: true,
  });

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 10, // Require the mouse to move by 10 pixels before activating
    },
  });
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 250, // Press delay of 250ms, with tolerance of 5px of movement
      tolerance: 5,
    },
  });

  const sensors = useSensors(mouseSensor, touchSensor);

  const {
    isOpen: isUploadOpen,
    open: onUploadOpen,
    close: onUploadClose,
  } = useDisclosure();

  const [draggingFolder, setDraggingFolder] =
    useState<DocExchangeFolder | null>(null);
  const [draggingFile, setDraggingFile] = useState<DocExchangeDocument | null>(
    null,
  );
  const [activePath, setActivePath] = useState("");

  const { mutateAsync: moveFolder, isPending: isMovingFolder } =
    useMoveFolder();

  const { mutateAsync: moveDocument, isPending: isMovingDocument } =
    useMoveDocument();

  const handleDragStart = (e: DragStartEvent) => {
    const unknownDraggingItem = e.active.data.current;

    if (unknownDraggingItem?.type === "document") {
      return setDraggingFile(unknownDraggingItem as DocExchangeDocument);
    }
    setDraggingFolder(unknownDraggingItem as DocExchangeFolder);
  };
  const handleDragEnd = async (e: DragEndEvent) => {
    const source = e.active.data.current;
    const target = e.over?.data.current as FolderWithPath | undefined;

    setDraggingFolder(null);
    setDraggingFile(null);

    // handle move document
    if (source?.type === "document") {
      const file = source as DocExchangeDocument;
      if (file?.parentDirectoryId === target?.id || !target?.id) return;

      await moveDocument({
        parentDirectoryId: target.id,
        documentId: file.id,
      });
      addAction({
        documentId: file.id,
        type: ROLLBACK_ACTIONS.moveDocument,
        newFolderId: target.id,
        oldFolderId: file.parentDirectoryId as string,
      });
      return;
    }

    // handle move folder
    const folder = source as FolderWithPath;

    if (
      (!folder.parentDirectoryId && !target?.id) || // Dragging a a top-level to the root
      folder.id === target?.id || // Dragging a folder onto itself
      folder.parentDirectoryId === target?.id || // Dragging a folder to its current parent
      target?.path?.startsWith(folder.path) // Dragging a parent folder into its own subfolder
    )
      return;

    await moveFolder({
      folderId: folder.id,
      parentDirectory: target?.id || null,
      siteId,
      studyId,
    });
    addAction({
      type: ROLLBACK_ACTIONS.moveFolder,
      oldFolderId: folder.parentDirectoryId,
      newFolderId: target?.id || null,
      folderId: folder.id,
    });
  };

  return (
    <>
      <Card className="[&>div]:p-2 sm:[&>div]:p-4">
        <div className="mb-4 flex flex-col justify-between gap-4 lg:flex-row lg:items-center">
          <h2 className="text-2xl font-semibold dark:text-white">
            Doc Exchange
          </h2>

          <div className="flex flex-col items-end justify-end gap-2 sm:flex-row">
            <div className="flex flex-1 justify-end gap-2.5">
              <div className="w-full sm:w-fit">
                <SearchField placeholder="Search all documents..." />
              </div>
              <FilterEBinder />
            </div>
            {!folderId ? (
              <Tooltip
                theme={{
                  content: "w-full sm:w-fit",
                  target: "w-full sm:w-fit",
                }}
                content="Select a folder to upload document"
              >
                <Button
                  className="w-full sm:w-fit"
                  disabled={!folderId}
                  variant="primary"
                >
                  <CiCirclePlus size={18} /> Upload Document
                </Button>
              </Tooltip>
            ) : (
              <Button
                className="w-full sm:w-fit"
                onClick={onUploadOpen}
                variant="primary"
              >
                <CiCirclePlus size={18} /> Upload Document
              </Button>
            )}
          </div>
        </div>
        <div className="flex flex-1 flex-col gap-4 ">
          <div className="flex flex-1 flex-col gap-4">
            <DndContext
              sensors={sensors}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              modifiers={[restrictToWindowEdges]}
            >
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="w-full md:max-w-[300px] lg:max-w-[320px] xl:max-w-[360px]">
                  <FolderColumn
                    draggingFolder={draggingFolder}
                    draggingFile={draggingFile}
                    isMovingFolder={isMovingFolder}
                    isMovingDocument={isMovingDocument}
                    activePath={activePath}
                    setActivePath={setActivePath}
                  />
                </div>
                <DocumentsTable
                  isMovingDocument={isMovingDocument}
                  draggingFile={draggingFile}
                />
              </div>

              <DragOverlay
                adjustScale={false}
                zIndex={9999}
                dropAnimation={null}
              >
                {!!draggingFile && <FileOverlay document={draggingFile} />}

                {!!draggingFolder && (
                  <FolderOverlay
                    folderName={draggingFolder?.name}
                    fileCount={draggingFolder?.fileCount}
                  />
                )}
              </DragOverlay>
            </DndContext>
          </div>
        </div>
      </Card>

      {isUploadOpen && (
        <UploadDocumentModal
          onCreatePlaceHolder={() => {
            onUploadClose();
          }}
          isOpen={isUploadOpen}
          onClose={onUploadClose}
          path={activePath}
        />
      )}
    </>
  );
};
