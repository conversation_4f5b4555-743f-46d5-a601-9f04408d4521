import {
  Dnd<PERSON>ontext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { useState } from "react";

import { TASK_STATUSES } from "@/components/ui/badges/task-status-badge";
import { Task } from "@/lib/apis/tasks";

import { useLocalStudyParams } from "../../../hooks/useLocalStudyParams";
import { useMoveTask } from "../hooks/use-task-mutations";
import { KanbanColumn } from "./kanban-column";
import { KanbanTask } from "./kanban-task";

export type MoveTaskPayload = {
  newStatus: string;
  task: Task;
};

export const TaskKanban = () => {
  const { studyId } = useLocalStudyParams();
  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      delay: 150,
      tolerance: 5,
    },
  });
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 250,
      tolerance: 8,
    },
  });
  const sensors = useSensors(mouseSensor, touchSensor);

  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const { mutateAsync: moveTask } = useMoveTask({
    studyId,
    taskId: activeTask?.id || "",
  });
  const handDragStart = (event: DragStartEvent) => {
    if (event.active.data.current) {
      setActiveTask(event.active.data.current as Task);
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    if (!event.over) return;
    const lastStatusIndex = TASK_STATUSES.length - 1;
    const overStatusIndex = TASK_STATUSES.findIndex(
      (s) => s.value === event.over?.id,
    );
    const activeStatusIndex = TASK_STATUSES.findIndex(
      (s) => s.value === event.active?.id.toString().split("_")[0],
    );
    const validColumn =
      overStatusIndex === lastStatusIndex ||
      overStatusIndex === activeStatusIndex + 1;

    const isSameColumn = event.active.id
      .toString()
      .includes(event.over?.id.toString());

    if (isSameColumn || !validColumn) return;

    await moveTask({
      newStatus: event.over.id.toString(),
      task: activeTask as Task,
    });
    setActiveTask(null);
  };

  return (
    <DndContext
      onDragEnd={handleDragEnd}
      onDragStart={handDragStart}
      sensors={sensors}
    >
      <div className="grid grid-cols-[repeat(4,_minmax(300px,_1fr))] gap-4 overflow-auto px-4">
        {TASK_STATUSES.map((status) => (
          <KanbanColumn
            key={status.value}
            label={status.label}
            status={status.value}
          />
        ))}
      </div>
      <DragOverlay>
        {activeTask ? (
          <KanbanTask task={activeTask} className="bg-white dark:bg-gray-700" />
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};
