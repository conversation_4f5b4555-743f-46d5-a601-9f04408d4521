import { <PERSON>pic<PERSON> } from "@clincove/shared-ui";
import { capitalize } from "lodash";
import { z } from "zod";

import { TASK_PRIORITIES } from "@/components/ui/badges/task-priority-badge";
import { TASK_STATUSES } from "@/components/ui/badges/task-status-badge";
import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { useInfiniteAssignableUsers } from "@/hooks/queries/use-infinite-assignable-users";
import { useInfiniteLocalStudyPatients } from "@/hooks/queries/use-infinite-local-study-patients";

import { useLocalStudyParams } from "../../hooks/useLocalStudyParams";
import { useTaskModals } from "./hooks/use-task-modals-store";
import { useCreateTask, useUpdateTask } from "./hooks/use-task-mutations";

export const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .trim()
    .min(1, "Name is required"),
  priority: z
    .string({ required_error: "Priority is required" })
    .min(1, "Priority is required"),
  status: z
    .string({ required_error: "Status is required" })
    .min(1, "Status is required"),
  description: z
    .string({ required_error: "Description is required" })
    .trim()
    .min(1, "Description is required"),
  dueDate: z
    .string({
      required_error: "Due Date is required",
      invalid_type_error: "Due Date is required",
    })
    .min(1, "Due Date is required"),
  assignedToProfileId: z.string().optional(),
  patientId: z.string().optional(),
});

type FormValues = z.infer<typeof schema>;

export const TaskModal = () => {
  const { siteId, studyId } = useLocalStudyParams();
  const { isOpenTaskModal, selectedTask, onCloseTaskModal, status } =
    useTaskModals();
  const isEditing = !!selectedTask;

  const { mutateAsync: createTask, isPending: isCreating } = useCreateTask({
    siteId,
    studyId,
  });
  const { mutateAsync: updateTask, isPending: isUpdating } = useUpdateTask({
    studyId,
    taskId: selectedTask?.id || "",
  });

  const cancelStatusIndex = TASK_STATUSES.length - 1;

  const cancelStatus = TASK_STATUSES[cancelStatusIndex];
  const currentStatusIndex = TASK_STATUSES.findIndex(
    (s) => s.value === selectedTask?.status,
  );
  const currentStatus = TASK_STATUSES[currentStatusIndex];
  const nextStatusIndex = currentStatusIndex + 1;
  const nextStatus = TASK_STATUSES[nextStatusIndex];
  const validStatusForUpdating =
    nextStatusIndex === cancelStatusIndex
      ? [currentStatus, cancelStatus]
      : [currentStatus, nextStatus, cancelStatus];

  const onSubmit = async (data: FormValues) => {
    isEditing
      ? await updateTask({
          ...data,
          id: selectedTask.id,
        })
      : await createTask(data);
    onCloseTaskModal();
  };

  return (
    <WrapperModal
      size="3xl"
      title={`${isEditing ? "Update" : "New"} Task`}
      isOpen={isOpenTaskModal}
      onClose={onCloseTaskModal}
    >
      <Form
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={{
          name: selectedTask?.name || "",
          dueDate: selectedTask?.dueDate || "",
          priority: selectedTask?.priority || "",
          status: selectedTask?.status || status || "",
          assignedToProfileId: selectedTask?.assignedTo?.profile?.id,
          patientId: selectedTask?.patientId ?? undefined,
          description: selectedTask?.description,
        }}
        className="grid gap-4 sm:grid-cols-2"
      >
        <div className="space-y-2">
          <Label htmlFor="name">Task Name</Label>
          <InputField placeholder="Enter name" name="name" id="name" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="dueDate">Due Date</Label>
          <Datepicker name="dueDate" id="dueDate" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="priority">Priority</Label>
          <Select
            placeholder="Select a priority"
            name="priority"
            id="priority"
            options={TASK_PRIORITIES.map((priority) => ({
              label: capitalize(priority),
              value: priority,
            }))}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            placeholder="Select a status"
            name="status"
            id="status"
            options={(isEditing ? validStatusForUpdating : TASK_STATUSES).map(
              (status) => ({
                label: status.label,
                value: status.value,
              }),
            )}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="patientId">Patient</Label>
          <LazySelect
            placeholder="Select a patient"
            name="patientId"
            id="patientId"
            useInfiniteQuery={useInfiniteLocalStudyPatients}
            getOptionLabel={(patient) => patient.name}
            getOptionValue={(patient) => patient.id}
            params={[
              {
                siteId,
                studyId,
              },
            ]}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="assignedToProfileId">Assigned To</Label>
          <LazySelect
            placeholder="Select assignee"
            name="assignedToProfileId"
            id="assignedToProfileId"
            useInfiniteQuery={useInfiniteAssignableUsers}
            getOptionLabel={(option) =>
              `${option.user.firstName} ${option.user.lastName}`
            }
            getOptionValue={(user) => user.id}
            params={[studyId]}
          />
        </div>
        <div className="space-y-2 sm:col-span-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            placeholder="Select a description"
            name="description"
            id="description"
          />
        </div>
        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:col-span-2 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onCloseTaskModal} />
          <Button
            type="submit"
            isLoading={isCreating || isUpdating}
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
