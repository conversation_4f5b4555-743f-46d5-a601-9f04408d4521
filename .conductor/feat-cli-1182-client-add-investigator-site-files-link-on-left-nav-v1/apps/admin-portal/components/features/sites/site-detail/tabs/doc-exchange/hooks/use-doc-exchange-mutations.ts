import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  CreateDocExchangeFolderPayload,
  EditDocExchangeDocumentPayload,
  UpdateDocExchangeFolderPayload,
  UpdateStatusFilesPayload,
  UploadMultipleFilesPayload,
} from "@/lib/apis/doc-exchange/types";
import { MoveFolderPayload } from "@/lib/apis/isf-folders";

import { docExchangeKeys } from "./use-doc-exchange-queries";
import { useFilterDocuments } from "./use-filter-documents";

export const useAddFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateDocExchangeFolderPayload) =>
      api.docExchange.createFolder(payload),
    onSuccess: (_, payload) => {
      return queryClient.invalidateQueries({
        queryKey: docExchangeKeys.folderList(payload.siteId),
      });
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Folder created successfully");
    },
    onError: (err) => {
      toast.error(err?.message || "Failed to create folder");
    },
  });
};

export const useUpdateFolder = (siteId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      payload: UpdateDocExchangeFolderPayload & { folderId: string },
    ) =>
      api.docExchange.updateFolder(payload.folderId, {
        name: payload.name,
      }),
    onSuccess: () => {
      return queryClient.invalidateQueries({
        queryKey: docExchangeKeys.folderList(siteId),
      });
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Folder updated successfully");
    },
    onError: () => {
      toast.error("Failed to update folder");
    },
  });
};

export const useDeleteFolder = (siteId: string) => {
  const queryClient = useQueryClient();
  const { folderId, setFolderId } = useFilterDocuments();
  return useMutation({
    mutationFn: (payload: { id: string; isHideToast?: boolean }) =>
      api.docExchange.archiveFolder(payload.id),

    onSuccess: (_, payload) => {
      if (folderId === payload.id) {
        setFolderId(null);
      }

      return queryClient.invalidateQueries({
        queryKey: docExchangeKeys.folderList(siteId),
      });
    },
    onSettled: (_, err, payload) => {
      if (!err && !payload.isHideToast)
        toast.success("Folder deleted successfully");
    },
    onError: (err) => toast.error(err?.message || "Something went wrong!"),
  });
};

export const useMoveFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: MoveFolderPayload) => {
      return api.docExchange.moveFolder(payload);
    },
    onSuccess: (_, payload) => {
      return queryClient.invalidateQueries({
        queryKey: docExchangeKeys.folderList(payload.siteId),
      });
    },
    onSettled: (_, err, payload) => {
      if (err) return;
      toast.success(
        payload.isRollback ? "Folder move undone" : "Folder moved successfully",
      );
    },
    onError: (err, payload) => {
      toast.error(
        err?.message || payload.isRollback
          ? "Failed to undo action"
          : "Failed to move folder",
      );
    },
  });
};

export const useUpdateDocument = (siteId: string) => {
  return useMutation({
    mutationFn: (
      payload: EditDocExchangeDocumentPayload & {
        documentId: string;
      },
    ) => api.docExchange.editDocument(payload),
    onSettled: (_, err) => {
      if (!err) toast.success("Update document successfully!");
    },
    onError: (err) => {
      toast.error(err?.message || "Fail to update document!");
    },
    meta: {
      awaits: [
        docExchangeKeys.allDocumentLists(),
        docExchangeKeys.folderList(siteId),
      ],
    },
  });
};

export const useDeleteDocument = () => {
  return useMutation({
    mutationFn: (payload: { id: string; isHideToast?: boolean }) =>
      api.docExchange.archiveDocument(payload.id),
    onSettled: (_, err, payload) => {
      if (!err && !payload.isHideToast)
        toast.success("Update document successfully!");
    },
    onError: (err) => {
      toast.error(err?.message || "Fail to update document!");
    },
    meta: {
      awaits: [
        docExchangeKeys.allDocumentLists(),
        docExchangeKeys.allFolderLists(),
      ],
    },
  });
};

export const useMoveDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      payload: EditDocExchangeDocumentPayload & {
        documentId: string;
        isRollback?: boolean;
      },
    ) => {
      return api.docExchange.editDocument(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: docExchangeKeys.allFolderLists(),
      });
      return queryClient.invalidateQueries({
        queryKey: docExchangeKeys.allDocumentLists(),
      });
    },
    onSettled: (_, err, payload) => {
      if (err) return;
      toast.success(
        payload.isRollback
          ? "Document move undone"
          : "Document moved successfully",
      );
    },
    onError: (err, payload) => {
      toast.error(
        err?.message || payload.isRollback
          ? "Failed to move document"
          : "Failed to undo folder",
      );
    },
  });
};

export const useUploadMultiple = () => {
  return useMutation({
    mutationFn: async (payload: UploadMultipleFilesPayload) =>
      api.docExchange.uploadMultipleFiles(payload),
  });
};

export const useUpdateFilesStatus = () => {
  return useMutation({
    mutationFn: (payload: UpdateStatusFilesPayload) =>
      api.docExchange.updateStatusFiles(payload),
  });
};

export const useUnarchiveFolder = (siteId: string) => {
  return useMutation({
    mutationFn: (folderId: string) => api.docExchange.restoreFolder(folderId),
    meta: {
      awaits: docExchangeKeys.folderList(siteId),
    },
  });
};

export const useUnarchiveDocument = (siteId: string) => {
  return useMutation({
    mutationFn: (documentId: string) =>
      api.docExchange.restoreDocument(documentId),
    meta: {
      awaits: [
        docExchangeKeys.allDocumentLists(),
        docExchangeKeys.folderList(siteId),
      ],
    },
  });
};
