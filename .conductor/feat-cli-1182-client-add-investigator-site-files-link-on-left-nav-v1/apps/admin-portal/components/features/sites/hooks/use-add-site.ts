import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddSitePayload } from "@/lib/apis/sites/types";

import { USE_SITES_QUERY_KEY } from "./use-sites";

export const useAddSite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddSitePayload) => api.sites.create(data),
    onSuccess: () => {
      toast.success("Site added successfully");
      queryClient.invalidateQueries({ queryKey: [USE_SITES_QUERY_KEY] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
