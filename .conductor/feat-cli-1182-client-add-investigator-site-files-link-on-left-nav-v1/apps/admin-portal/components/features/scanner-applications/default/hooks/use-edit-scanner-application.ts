import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateScannerApplicationPayload } from "@/lib/apis/scanner-applications";

export const useEditScannerApplication = () => {
  return useMutation({
    mutationFn: async (
      data: UpdateScannerApplicationPayload & { id: string },
    ) => {
      const { id, ...payload } = data;
      return api.scannerApplications.update(id, payload);
    },
    onError: (error) => {
      toast.error(error.message || "Failed to edit scanner application");
    },
  });
};
