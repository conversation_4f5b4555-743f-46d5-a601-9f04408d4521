import { Datepicker } from "@clincove/shared-ui";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Table, TableLoading } from "@/components/ui/table";
import { useInfiniteUsers } from "@/hooks/queries/use-infinite-users";
import { Enrollment } from "@/lib/apis/training-modules";
import { cn } from "@/lib/utils";

import {
  useAddEnrollment,
  useUpdateEnrollment,
} from "../../../hooks/use-training-module-mutations";
import { useChapterProgressesByEnrollmentId } from "../../../hooks/use-training-module-queries";
import { chapterProgressColumns } from "./columns";

export const ENROLLMENT_STATUSES = [
  {
    label: "Not Started",
    value: "not_started",
  },
  {
    label: "In Progress",
    value: "in_progress",
  },
  {
    label: "Completed",
    value: "completed",
  },
  {
    label: "Cancelled",
    value: "cancelled",
  },
] as const;

const schema = z.object({
  status: z.enum(["not_started", "in_progress", "completed", "cancelled"], {
    errorMap: () => ({
      message: "Status is required",
    }),
  }),
  userId: z
    .string({
      required_error: "User is required",
      invalid_type_error: "User is required",
    })
    .min(1, "User is required"),
  startedAt: z.string().optional().nullable(),
  completedAt: z.string().optional().nullable(),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedEnrollment: Enrollment | null;
};

export const EnrollmentModal = function ({
  isOpen,
  onClose,
  selectedEnrollment,
}: Props) {
  const courseId = useParams().id as string;
  const { mutateAsync: addEnrollment, isPending: isAdding } =
    useAddEnrollment(courseId);
  const { mutateAsync: updateEnrollment, isPending: isUpdating } =
    useUpdateEnrollment(courseId);
  const isEditing = !!selectedEnrollment;

  const { data, isPending } = useChapterProgressesByEnrollmentId(
    selectedEnrollment?.id,
  );

  const methods = useForm<FormValues>({
    mode: "onChange",
    resolver: zodResolver(schema),
    defaultValues: {
      userId: selectedEnrollment?.userId || "",
      status: selectedEnrollment?.status,
      startedAt: selectedEnrollment?.startedAt || "",
      completedAt: selectedEnrollment?.completedAt || "",
    },
  });

  const startedAt = methods.watch("startedAt");

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateEnrollment({
          ...data,
          id: selectedEnrollment.id,
          progress: {},
        })
      : await addEnrollment({ ...data, progress: {} });
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "Add"} Enrollment`}
    >
      <Form
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        formMethods={methods}
        className="mb-2"
      >
        <div className="grid gap-4 sm:grid-cols-2 sm:gap-6">
          <div className={cn("space-y-2", isEditing && "hidden")}>
            <Label htmlFor="userId">User</Label>
            <LazySelect
              name="userId"
              id="userId"
              searchPlaceholder="Search user..."
              useInfiniteQuery={useInfiniteUsers}
              getOptionLabel={(user) => `${user.firstName} ${user.lastName}`}
              getOptionValue={(user) => user.id}
              placeholder="Select user"
            />
          </div>

          <div className={cn("space-y-2", isEditing && "sm:col-span-2")}>
            <Label htmlFor="status">Status</Label>
            <Select
              id="status"
              name="status"
              placeholder="Select a status..."
              options={ENROLLMENT_STATUSES.map((status) => ({
                label: status.label,
                value: status.value,
              }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="startedAt">Started Date</Label>
            <Datepicker
              id="startedAt"
              name="startedAt"
              placeholder="Select started date..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="completedAt">Completed Date</Label>
            <Datepicker
              id="completedAt"
              name="completedAt"
              placeholder="Select completed date..."
              minDate={startedAt ? new Date(startedAt) : undefined}
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            disabled={isAdding || isUpdating}
            isLoading={isAdding || isUpdating}
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
      {isEditing &&
        (isPending ? (
          <TableLoading columns={chapterProgressColumns} />
        ) : (
          <>
            <Table
              columns={chapterProgressColumns}
              data={data?.results ?? []}
            />
          </>
        ))}
    </WrapperModal>
  );
};
