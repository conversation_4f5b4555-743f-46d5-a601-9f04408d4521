import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import {
  TableRemoveButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Course } from "@/lib/apis/training-modules";

import { COURSE_STATUSES } from "./course-modal";

export const COURSE_STATUS_VARIANTS = {
  draft: "warning",
  published: "success",
  archived: "danger",
} as const;

type Props = {
  onDelete: (course: Course) => void;
};

export const generateCourseColumns = ({
  onDelete,
}: Props): ColumnDef<Course>[] => [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => {
      return (
        <Link
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
          href={`/training/courses/${row.original?.id}`}
        >
          {row.original.name}
        </Link>
      );
    },
  },
  {
    header: "Description",
    accessorKey: "description",
    enableSorting: false,
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.original.status;
      if (COURSE_STATUSES.includes(status))
        return (
          <PillBadge
            className="capitalize"
            variant={COURSE_STATUS_VARIANTS[status]}
          >
            {status}
          </PillBadge>
        );
      return null;
    },
  },
  {
    header: "Order",
    accessorKey: "order",
  },
  {
    header: "Module",
    id: "module.name",
    accessorKey: "module.name",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-4">
          <TableViewButton
            type="link"
            href={`/training/courses/${row.original?.id}`}
          />
          <TableRemoveButton onClick={() => onDelete(row.original)} />
        </div>
      );
    },
  },
];
