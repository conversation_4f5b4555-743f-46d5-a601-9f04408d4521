"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Card } from "flowbite-react";
import { useMemo, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { FleetOverviewScanner } from "@/lib/apis/scanners";

import { useFleetOverview } from "./hooks/use-device-management-queries";
import { PreviewDiagnosticsModal } from "./preview-diagnostics";

const generateColumns = ({
  onPreview,
}: {
  onPreview: (id: string) => void;
}): ColumnDef<FleetOverviewScanner>[] => [
  {
    accessorKey: "displayName",
    header: "Scanner Name",
    cell: ({ row }) => (
      <button
        className="text-primary-500 flex cursor-pointer flex-col text-left hover:underline"
        onClick={() => onPreview(row.original.id)}
      >
        {row.original.displayName}
      </button>
    ),
  },
  {
    accessorKey: "siteName",
    header: "Site",
  },
  {
    accessorKey: "modelName",
    header: "Model",
  },
  {
    accessorKey: "ipAddress",
    header: "IP Address",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => (
      <PillBadge variant={row.original.isActive ? "success" : "danger"}>
        {row.original.isActive ? "Active" : "Inactive"}
      </PillBadge>
    ),
  },
  {
    accessorKey: "lastCheckIn",
    header: "Last Check-in",
    cell: ({ row }) => {
      const date = new Date(row.original.lastCheckIn);
      return (
        <div className="text-sm text-gray-700 dark:text-gray-300">
          {date.toLocaleDateString()} {date.toLocaleTimeString()}
        </div>
      );
    },
  },
  {
    accessorKey: "currentAppVersion",
    header: "Current Version",
  },
  {
    accessorKey: "targetAppVersion",
    header: "Target Version",
  },
];

export const ScannerFleetOverview = () => {
  const { data, isPending, isPlaceholderData } = useFleetOverview();
  const [scannerId, setScannerId] = useState("");

  const columns = useMemo(
    () =>
      generateColumns({
        onPreview: (id) => setScannerId(id),
      }),
    [],
  );

  return (
    <>
      <div>
        <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
          Scanner Fleet Overview
        </h3>

        <Card className="[&>div]:p-0">
          {isPending ? (
            <TableLoading columns={columns} />
          ) : (
            <LoadingWrapper isLoading={isPlaceholderData}>
              <Table data={data?.results ?? []} columns={columns} />
              {/* {data?.metadata && (
              <TableDataPagination
                isUseExternalState
                page={page}
                setPage={setPage}
                metadata={data.metadata}
              />
            )} */}
            </LoadingWrapper>
          )}
        </Card>
      </div>

      {scannerId && (
        <PreviewDiagnosticsModal
          id={scannerId}
          isOpen={!!scannerId}
          onClose={() => setScannerId("")}
        />
      )}
    </>
  );
};
