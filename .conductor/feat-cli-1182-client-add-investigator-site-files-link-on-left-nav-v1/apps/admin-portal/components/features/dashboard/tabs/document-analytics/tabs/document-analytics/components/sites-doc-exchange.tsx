"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Card, TextInput } from "flowbite-react";
import Link from "next/link";
import { useState } from "react";
import { HiSearch } from "react-icons/hi";
import { useDebounceCallback } from "usehooks-ts";

import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { SiteDocExchange } from "@/lib/apis/document-analytics";

import { useSiteDocExchange } from "../hooks/use-document-analytics-queries";

const columns: ColumnDef<SiteDocExchange>[] = [
  {
    accessorKey: "siteName",
    header: "Site Name",
    cell: ({ row }) => (
      <Link
        href={`/sites/${row.original.siteId}`}
        className="text-primary-500 flex cursor-pointer flex-col text-left hover:underline"
      >
        {row.original.siteName}
      </Link>
    ),
  },
  {
    accessorKey: "numberDocExchange",
    header: "Total Doc Exchange",
  },
];

export const SitesDocExchangeTable = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");

  const debounced = useDebounceCallback((value) => {
    setSearch(value);
    setCurrentPage(1);
  }, 300);
  const { data, isPending } = useSiteDocExchange(currentPage, search);

  return (
    <Card className="[&>div]:p-0">
      <div className="flex items-center justify-between p-4">
        <h3 className=" text-lg font-semibold text-gray-900 dark:text-white">
          Site Doc Exchange Statistics
        </h3>
        <div className="max-w-80">
          <TextInput
            className="w-full text-sm"
            icon={HiSearch}
            sizing="sm"
            placeholder="Search..."
            onChange={(e) => debounced(e.target.value)}
          />
        </div>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <>
          <Table columns={columns} data={data?.results ?? []} />
          {data?.metadata && (
            <TableDataPagination
              metadata={data?.metadata}
              page={currentPage}
              setPage={setCurrentPage}
            />
          )}
        </>
      )}
    </Card>
  );
};
