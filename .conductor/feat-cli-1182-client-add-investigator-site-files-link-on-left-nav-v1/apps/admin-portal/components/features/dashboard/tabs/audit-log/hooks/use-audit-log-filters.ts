import { endOfDay, startOfDay } from "date-fns";
import { parseAsArrayOf, parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";

export type AuditLogFilterParams = {
  userId?: string;
  startDate?: string;
  endDate?: string;
  actions?: string[];
  resourceTypes?: string[];
};

export const useAuditLogFilters = () => {
  const { page, take, goToPage } = usePagination();

  const [userId, setUserId] = useQueryState("userId", parseAsString);
  const [fromDate, setFromDate] = useQueryState("fromDate", parseAsString);
  const [toDate, setToDate] = useQueryState("toDate", parseAsString);
  const [actions, setActions] = useQueryState(
    "actions",
    parseAsArrayOf(parseAsString),
  );
  const [resourceTypes, setResourceTypes] = useQueryState(
    "resourceTypes",
    parseAsArrayOf(parseAsString),
  );

  const clearAllFilters = () => {
    setUserId(null);
    setFromDate(null);
    setToDate(null);
    setActions(null);
    setResourceTypes(null);
    goToPage(1);
  };

  const params = {
    page,
    take,
    filter: {
      userId: userId || undefined,
      startDate: fromDate ? startOfDay(fromDate) : undefined,
      endDate: toDate ? endOfDay(toDate) : undefined,
      actions: actions && actions.length > 0 ? actions : undefined,
      resourceTypes:
        resourceTypes && resourceTypes.length > 0 ? resourceTypes : undefined,
    },
  };

  return {
    userId,
    fromDate,
    toDate,
    actions,
    resourceTypes,

    setUserId,
    setFromDate,
    setToDate,
    setActions,
    setResourceTypes,
    clearAllFilters,
    goToPage,

    params,
  };
};
