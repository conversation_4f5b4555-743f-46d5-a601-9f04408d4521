import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { PatientInactive } from "@/lib/apis/data-integrity";

export const patientsInactiveColumns: ColumnDef<PatientInactive>[] = [
  {
    accessorKey: "id",
    header: "Patient ID",
    cell: ({ row }) =>
      row.original.siteId && row.original.studyId ? (
        <Link
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
          href={`/sites/${row.original.siteId}/studies/${row.original.studyId}/patients/${row.original.id}`}
        >
          {row.original.id}
        </Link>
      ) : (
        row.original.id
      ),
  },
  {
    accessorKey: "name",
    header: "Patient Name",
  },
];
