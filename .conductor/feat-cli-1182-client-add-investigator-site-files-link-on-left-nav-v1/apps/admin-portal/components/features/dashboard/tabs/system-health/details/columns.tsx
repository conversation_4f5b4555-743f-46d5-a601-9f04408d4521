import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import {
  HmacFailureRecord,
  ProcessingErrorRecord,
} from "@/lib/apis/system-health";
import { formatDate } from "@/lib/utils";
import { snakeCaseToCapitalized } from "@/utils/string";

export const hmacFailuresColumns: ColumnDef<HmacFailureRecord>[] = [
  {
    accessorKey: "source",
    header: "Source",
    cell: ({ row }) => snakeCaseToCapitalized(row.original.source),
  },
  {
    accessorKey: "message",
    header: "Message",
  },
  {
    accessorKey: "relatedName",
    header: "Related Document",
    cell: ({ row }) =>
      row.original.source === "source_document"
        ? row.original.patientId &&
          row.original.relatedName &&
          row.original.siteId &&
          row.original.studyId && (
            <Link
              href={`/sites/${row.original.siteId}/studies/${row.original.studyId}/patients/${row.original.patientId}?tab=consents`}
              className="text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
            >
              {row.original.relatedName}
            </Link>
          )
        : row.original.source === "artifact_version"
          ? row.original.relatedName &&
            row.original.relatedId &&
            row.original.siteId &&
            row.original.studyId &&
            row.original.isfFolderId && (
              <Link
                href={`/sites/${row.original.siteId}/studies/${row.original.studyId}/?tab=isf&folderId=${row.original.isfFolderId}`}
                className="text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
              >
                {row.original.relatedName}
              </Link>
            )
          : row.original.relatedName,
  },
  {
    accessorKey: "patientName",
    header: "Patient",
    cell: ({ row }) =>
      row.original.patientId &&
      row.original.patientName && (
        <Link
          href={`/sites/${row.original.siteId}/studies/${row.original.studyId}/patients/${row.original.patientId}`}
          className="text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
        >
          {row.original.patientName || "-"}
        </Link>
      ),
  },
  {
    accessorKey: "timestamp",
    header: "Timestamp",
    cell: ({ row }) => formatDate(row.original.timestamp),
  },
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case "pending":
      return <PillBadge variant="warning">Pending</PillBadge>;
    case "error":
      return <PillBadge variant="danger">Error</PillBadge>;
    case "resolved":
      return <PillBadge variant="success">Resolved</PillBadge>;
    default:
      return <PillBadge variant="default">{status}</PillBadge>;
  }
};

export const processingErrorsColumns: ColumnDef<ProcessingErrorRecord>[] = [
  {
    accessorKey: "timestamp",
    header: "Timestamp",
    cell: ({ row }) => formatDate(row.original.timestamp),
  },
  {
    accessorKey: "source",
    header: "Source",
    cell: ({ row }) => snakeCaseToCapitalized(row.original.source),
  },
  {
    accessorKey: "message",
    header: "Message",
  },
  {
    accessorKey: "relatedName",
    header: "Related Document",
  },
  {
    accessorKey: "patientName",
    header: "Patient",
  },
  {
    accessorKey: "studyName",
    header: "Study",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => getStatusBadge(row.original.status),
  },
];
