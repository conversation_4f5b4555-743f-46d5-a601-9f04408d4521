import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { StudyNoProtocol } from "@/lib/apis/data-integrity";

export const studiesNoProtocolColumns: ColumnDef<StudyNoProtocol>[] = [
  {
    accessorKey: "id",
    header: "Study ID",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/studies/${row.original.id}`}
      >
        {row.original.id}
      </Link>
    ),
  },
  {
    accessorKey: "name",
    header: "Study Name",
  },
];
