import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const useUserManagementKeys = {
  all: () => ["user-management"] as const,

  userStatistics: () => [...useUserManagementKeys.all(), "statistics"] as const,
  roleDistribution: () =>
    [...useUserManagementKeys.all(), "role-distribution"] as const,

  allLoginActivitiesList: () =>
    [...useUserManagementKeys.all(), "login-activities"] as const,
  loginActivitiesList: (params?: { fromDate?: string; toDate?: string }) =>
    [...useUserManagementKeys.allLoginActivitiesList(), params] as const,

  allUserWithoutProfileList: () =>
    [...useUserManagementKeys.all(), "users-without-profile"] as const,
  userWithoutProfileList: (params?: MetadataParams) =>
    [...useUserManagementKeys.allUserWithoutProfileList(), params] as const,

  allProfileWithoutRoleList: () =>
    [...useUserManagementKeys.all(), "profiles-without-role"] as const,
  profileWithoutRoleList: (params?: MetadataParams) =>
    [...useUserManagementKeys.allProfileWithoutRoleList(), params] as const,

  allUsersNeverLoginList: () =>
    [...useUserManagementKeys.all(), "users-never-login"] as const,
  usersNeverLoginList: (params?: MetadataParams) =>
    [...useUserManagementKeys.allUsersNeverLoginList(), params] as const,
};

export const TAKE_50 = 50;

export const useUserStatistic = () => {
  return useQuery({
    queryKey: useUserManagementKeys.userStatistics(),
    queryFn: () => api.userManagement.getUserStatistic(),
  });
};

export const useLoginActivities = (payload?: {
  fromDate?: string;
  toDate?: string;
}) => {
  return useQuery({
    queryKey: useUserManagementKeys.loginActivitiesList(payload),
    queryFn: () => api.userManagement.getLoginActivity(payload),
    placeholderData: (prev) => prev,
    enabled: !!payload?.fromDate && !!payload?.toDate,
  });
};

export const useRoleDistribution = () => {
  return useQuery({
    queryKey: useUserManagementKeys.roleDistribution(),
    queryFn: () => api.userManagement.getRoleDistribution(),
  });
};

export const useUsersWithoutProfile = (page: number) => {
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useUserManagementKeys.userWithoutProfileList(params),
    queryFn: () => api.userManagement.getUsersWithoutProfile(params),
    placeholderData: (prev) => prev,
  });
};

export const useProfilesWithoutRole = (page: number) => {
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useUserManagementKeys.profileWithoutRoleList(params),
    queryFn: () => api.userManagement.getProfilesWithoutRole(params),
    placeholderData: (prev) => prev,
  });
};

export const useUsersNeverLogin = (page: number) => {
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useUserManagementKeys.usersNeverLoginList(params),
    queryFn: () => api.userManagement.getUsersNeverLogin(params),
    placeholderData: (prev) => prev,
  });
};
