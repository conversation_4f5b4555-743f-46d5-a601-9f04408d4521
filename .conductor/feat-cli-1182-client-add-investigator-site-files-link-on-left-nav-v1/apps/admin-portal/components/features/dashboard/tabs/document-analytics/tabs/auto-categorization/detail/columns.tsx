import { ColumnDef } from "@tanstack/react-table";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import { AutoCategorizationRecord } from "@/lib/apis/categorization-analytics";
import { formatDate } from "@/lib/utils";
import { snakeCaseToCapitalized } from "@/utils/string";

export const columns: ColumnDef<AutoCategorizationRecord>[] = [
  {
    accessorKey: "fileName",
    header: "File Name",
    cell: ({ row }) => {
      return <span className="break-words">{row.original.fileName}</span>;
    },
  },
  {
    accessorKey: "study",
    header: "Study",
  },
  {
    accessorKey: "site",
    header: "Site",
  },
  {
    accessorKey: "method",
    header: "Method",
    cell: ({ row }) => snakeCaseToCapitalized(row.original.method),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      if (!status) return "-";
      const variant =
        status === "success"
          ? "success"
          : status === "failed"
            ? "danger"
            : "warning";
      return (
        <PillBadge variant={variant}>
          {snakeCaseToCapitalized(status)}
        </PillBadge>
      );
    },
  },
  {
    accessorKey: "confidenceScore",
    header: "Confidence Score",
  },
  {
    accessorKey: "dateTime",
    header: "Date Time",
    cell: ({ row }) =>
      row.original.dateTime
        ? formatDate(row.original.dateTime, "LLL dd, yyyy")
        : "-",
  },
  {
    accessorKey: "reason",
    header: "Reason",
    enableSorting: false,
    cell: ({ row }) => {
      const reason = row.getValue("reason") as string;
      return (
        <span className="break-words" title={reason}>
          {reason || "—"}
        </span>
      );
    },
  },
];
