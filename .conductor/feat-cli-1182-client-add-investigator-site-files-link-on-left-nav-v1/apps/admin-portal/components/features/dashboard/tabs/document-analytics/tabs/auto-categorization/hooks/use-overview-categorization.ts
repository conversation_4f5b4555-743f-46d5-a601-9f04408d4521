import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { AutoCategorizationParams } from "@/lib/apis/categorization-analytics";

import { useOverviewCategorizationFilters } from "./use-overview-categorization-filters";

export const autoCategoryKeys = {
  all: () => ["auto-categorization"] as const,

  methodDistribution: (params?: AutoCategorizationParams) =>
    [...autoCategoryKeys.all(), "method-distribution", params] as const,

  methodTrends: (params?: AutoCategorizationParams) =>
    [...autoCategoryKeys.all(), "method-trends", params] as const,

  aiSuccessRate: (params?: AutoCategorizationParams) =>
    [...autoCategoryKeys.all(), "ai-success-rate", params] as const,

  failureRate: (params?: AutoCategorizationParams) =>
    [...autoCategoryKeys.all(), "failure-rate", params] as const,

  conversionRate: (params?: AutoCategorizationParams) =>
    [...autoCategoryKeys.all(), "conversion-rate", params] as const,

  aiSuccessRateTrend: (params?: AutoCategorizationParams) =>
    [...autoCategoryKeys.all(), "ai-success-rate-trend", params] as const,
};

export const useMethodDistribution = () => {
  const { params } = useOverviewCategorizationFilters();

  return useQuery({
    queryKey: autoCategoryKeys.methodDistribution(params),
    queryFn: () => api.categorizationAnalyticsApi.getMethodDistribution(params),
    placeholderData: (prev) => prev,
  });
};

export const useMethodTrends = ({
  granularity,
}: {
  granularity: "day" | "week";
}) => {
  const { params } = useOverviewCategorizationFilters();

  return useQuery({
    queryKey: autoCategoryKeys.methodTrends({ ...params, granularity }),
    queryFn: () =>
      api.categorizationAnalyticsApi.getMethodTrends({
        ...params,
        granularity,
      }),
    placeholderData: (prev) => prev,
  });
};

export const useAiSuccessRate = () => {
  const { params } = useOverviewCategorizationFilters();

  return useQuery({
    queryKey: autoCategoryKeys.aiSuccessRate(params),
    queryFn: () => api.categorizationAnalyticsApi.getAiSuccessRate(params),
    placeholderData: (prev) => prev,
  });
};

export const useFailureRate = () => {
  const { params } = useOverviewCategorizationFilters();

  return useQuery({
    queryKey: autoCategoryKeys.failureRate(params),
    queryFn: () => api.categorizationAnalyticsApi.getFailureRate(params),
    placeholderData: (prev) => prev,
  });
};

export const useConversionRate = () => {
  const { params } = useOverviewCategorizationFilters();

  return useQuery({
    queryKey: autoCategoryKeys.conversionRate(params),
    queryFn: () => api.categorizationAnalyticsApi.getConversionRate(params),
    placeholderData: (prev) => prev,
  });
};

export const useAiSuccessRateTrend = () => {
  const { params } = useOverviewCategorizationFilters();

  return useQuery({
    queryKey: autoCategoryKeys.aiSuccessRateTrend(params),
    queryFn: () => api.categorizationAnalyticsApi.getAiSuccessRateTrend(params),
    placeholderData: (prev) => prev,
  });
};
