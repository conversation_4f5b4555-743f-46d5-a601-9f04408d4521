"use client";

import { Card } from "flowbite-react";
import { parseAsString, useQueryState } from "nuqs";
import { FiDatabase, FiFile, FiShare2 } from "react-icons/fi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { usePagination } from "@/hooks/use-pagination";
import { cn } from "@/lib/utils";
import { snakeCaseToCapitalized } from "@/utils/string";

import {
  useAllDocuments,
  usePendingDocuments,
} from "../hooks/use-document-analytics-queries";
import { documentColumns } from "./columns";
import { DocumentAnalyticsTableFilter } from "./document-analytics-table-filter";

const DOCUMENT_TYPE_OPTIONS = [
  {
    value: "source_documents",
    icon: FiFile,
  },
  {
    value: "artifact_files",
    icon: FiDatabase,
  },
  {
    value: "doc_exchange_files",
    icon: FiShare2,
  },
] as const;

type DocumentAnalyticsTableProps = {
  type: "all" | "pending";
};

export const DocumentAnalyticsTable = ({
  type,
}: DocumentAnalyticsTableProps) => {
  const { goToPage } = usePagination();
  const [sourceType, setSourceType] = useQueryState(
    "sourceType",
    parseAsString.withDefault("source_documents"),
  );

  const useDocumentsHook =
    type === "all" ? useAllDocuments : usePendingDocuments;
  const {
    data: documentsData,
    isPending: isLoading,
    isPlaceholderData,
  } = useDocumentsHook();

  return (
    <Card className="[&>div]:p-0">
      <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex flex-wrap gap-2">
          {DOCUMENT_TYPE_OPTIONS.map((option) => {
            const Icon = option.icon;
            const isActive = sourceType === option.value;

            return (
              <Button
                key={option.value}
                variant={isActive ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setSourceType(option.value);
                  goToPage(1);
                }}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 transition-all duration-200",
                  isActive
                    ? "border-blue-600 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700"
                    : "border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-700",
                )}
              >
                <Icon className="h-4 w-4" />
                <span className="font-medium">
                  {snakeCaseToCapitalized(option.value)}
                </span>
              </Button>
            );
          })}
        </div>
        <DocumentAnalyticsTableFilter />
      </div>

      {isLoading ? (
        <TableLoading columns={documentColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <>
            <Table
              columns={documentColumns}
              data={documentsData?.results ?? []}
            />

            {documentsData?.metadata && (
              <TableDataPagination metadata={documentsData?.metadata} />
            )}
          </>
        </LoadingWrapper>
      )}
    </Card>
  );
};
