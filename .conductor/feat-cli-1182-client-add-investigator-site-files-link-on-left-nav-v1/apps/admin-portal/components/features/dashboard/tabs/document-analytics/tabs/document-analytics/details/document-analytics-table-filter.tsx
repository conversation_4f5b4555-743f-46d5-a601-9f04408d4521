"use client";

import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { useInfiniteSponsors } from "@/components/features/sponsors/hooks/use-sponsors";
import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef } from "@/components/ui/form";
import { DateRangePicker } from "@/components/ui/form/date-picker/date-range-picker";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { useInfiniteStudiesBySponsor } from "@/hooks/queries/use-infinite-studies";
import { cn } from "@/lib/utils";

import { useDocumentAnalyticsFilters } from "../hooks/use-document-analytics-filters";

const schema = z.object({
  sponsorId: z.string().optional().nullable(),
  studyId: z.string().optional().nullable(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const DocumentAnalyticsTableFilter = () => {
  const [open, setOpen] = useState(false);

  const {
    sponsorId,
    setSponsorId,
    studyId,
    setStudyId,
    fromDate,
    setFromDate,
    toDate,
    setToDate,
    goToPage,
  } = useDocumentAnalyticsFilters();

  const [selectedSponsorId, setSelectedSponsorId] = useState(sponsorId || "");

  const getActiveFilterCount = () => {
    let count = 0;
    if (sponsorId) count++;
    if (studyId) count++;
    if (fromDate || toDate) count++;
    return count;
  };

  const formRef = useRef<FormRef<typeof schema>>(null);

  const handleApplyFilters = (values: z.infer<typeof schema>) => {
    setSponsorId(values.sponsorId || null);
    setStudyId(values.studyId || null);
    setFromDate(values.dateRange?.from?.toISOString() || null);
    setToDate(values.dateRange?.to?.toISOString() || null);
    setOpen(false);
    goToPage(1);
  };

  const handleClearFilters = () => {
    setSponsorId(null);
    setStudyId(null);
    setFromDate(null);
    setToDate(null);
    formRef.current?.formHandler.reset();
    setSelectedSponsorId("");
    setOpen(false);
    goToPage(1);
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Dropdown open={open} onOpenChange={setOpen}>
      <DropdownTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "relative flex items-center gap-2 px-3 py-2",
            "border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50",
            "dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-700",
          )}
        >
          <ListFilter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <span className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </DropdownTrigger>

      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <Form
          schema={schema}
          mode="onChange"
          onSubmit={handleApplyFilters}
          className="w-80 md:w-96"
          ref={formRef}
          defaultValues={{
            sponsorId: sponsorId || "",
            studyId: studyId || "",
            dateRange: {
              from: fromDate ? new Date(fromDate) : undefined,
              to: toDate ? new Date(toDate) : undefined,
            },
          }}
        >
          <div className="flex flex-col divide-y">
            <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
              <Label
                htmlFor="sponsorId"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
              >
                Sponsor
              </Label>
              <LazySelect
                name="sponsorId"
                useInfiniteQuery={useInfiniteSponsors}
                getOptionLabel={(sponsor) => sponsor.name}
                getOptionValue={(sponsor) => sponsor.id}
                placeholder="Select sponsor"
                searchPlaceholder="Search sponsors..."
                onSelect={(value) => {
                  setSelectedSponsorId(value);
                }}
              />
            </div>

            <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
              <Label
                htmlFor="studyId"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
              >
                Study
              </Label>
              <LazySelect
                name="studyId"
                useInfiniteQuery={useInfiniteStudiesBySponsor}
                params={[selectedSponsorId]}
                getOptionLabel={(study) => study.name}
                getOptionValue={(study) => study.id}
                placeholder="Select study"
                searchPlaceholder="Search studies..."
                dependentFieldNames={["sponsorId"]}
              />
            </div>

            <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
              <Label
                htmlFor="dateRange"
                className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2 dark:text-gray-300"
              >
                Date Range
              </Label>
              <DateRangePicker
                name="dateRange"
                placeholder="Select date range"
                maxDate={new Date()}
              />
            </div>
          </div>

          <div className="flex justify-end gap-5 px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Button
              variant="outline"
              className={cn(
                "w-full justify-center",
                activeFilterCount === 0 && "pointer-events-none invisible",
              )}
              onClick={handleClearFilters}
              type="button"
            >
              Clear Filters
            </Button>
            <Button
              variant="primary"
              type="submit"
              className="w-full justify-center"
            >
              Apply Filters
            </Button>
          </div>
        </Form>
      </DropdownContent>
    </Dropdown>
  );
};
