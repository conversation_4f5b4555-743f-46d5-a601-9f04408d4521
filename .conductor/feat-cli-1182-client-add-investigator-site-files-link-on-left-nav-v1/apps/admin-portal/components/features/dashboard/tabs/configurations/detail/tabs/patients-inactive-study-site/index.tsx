"use client";

import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { patientsInactiveColumns } from "./columns";
import { usePatientsInactive } from "./use-patients-inactive-queries";

export const PatientsInactiveStudySiteTab = () => {
  const {
    data: patientsData,
    isPending: isLoadingPatients,
    isPlaceholderData: isPatientsPlaceholderData,
  } = usePatientsInactive();

  return (
    <div>
      <Card className="[&>div]:p-0">
        <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
          Patients in Inactive Study/Site
        </h4>

        {isLoadingPatients ? (
          <TableLoading columns={patientsInactiveColumns} />
        ) : (
          <LoadingWrapper isLoading={isPatientsPlaceholderData}>
            <Table
              columns={patientsInactiveColumns}
              data={patientsData?.results || []}
            />

            {patientsData?.metadata && (
              <TableDataPagination metadata={patientsData.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
