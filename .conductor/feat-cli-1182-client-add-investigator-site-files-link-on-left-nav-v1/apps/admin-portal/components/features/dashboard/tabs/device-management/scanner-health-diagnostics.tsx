"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Card } from "flowbite-react";
import { useState } from "react";
import { FiActivity } from "react-icons/fi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { Scanner } from "@/lib/apis/scanners";

import { useScannerHealthDiagnostics } from "./hooks/use-device-management-queries";

const columns: ColumnDef<Scanner>[] = [
  {
    accessorKey: "displayName",
    header: "Scanner Name",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => (
      <div className="flex items-center">
        <div
          className={`mr-2 h-2 w-2 rounded-full ${
            row.original.isActive ? "bg-green-500" : "bg-red-500"
          }`}
        />
        <span
          className={`text-sm font-medium ${
            row.original.isActive
              ? "text-green-700 dark:text-green-400"
              : "text-red-700 dark:text-red-400"
          }`}
        >
          {row.original.isActive ? "Active" : "Inactive"}
        </span>
      </div>
    ),
  },
  {
    accessorKey: "ipAddress",
    header: "IP Address",
    cell: ({ row }) => (
      <div className="font-mono text-sm text-gray-700 dark:text-gray-300">
        {row.original.ipAddress || "N/A"}
      </div>
    ),
  },
  {
    accessorKey: "lastUpdate",
    header: "Last Update",
    cell: ({ row }) => {
      if (!row.original.lastUpdate) {
        return <span className="text-gray-500 dark:text-gray-400">Never</span>;
      }
      const date = new Date(row.original.lastUpdate);
      return (
        <div className="text-sm text-gray-700 dark:text-gray-300">
          {date.toLocaleDateString()} {date.toLocaleTimeString()}
        </div>
      );
    },
  },
  {
    accessorKey: "currentVersion",
    header: "Current Version",
    cell: ({ row }) => (
      <div className="font-mono text-sm text-gray-700 dark:text-gray-300">
        {row.original.currentVersion?.versionNumber || "N/A"}
      </div>
    ),
  },
];

export const ScannerHealthDiagnostics = () => {
  const [page, setPage] = useState(1);
  const { data, isPending, isPlaceholderData } =
    useScannerHealthDiagnostics(page);

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        Scanner Health Diagnostics
      </h3>

      <Card className="[&>div]:p-6">
        <div className="mb-4 flex items-center gap-3">
          <FiActivity className="h-5 w-5 text-orange-500" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">
            Device Health & Status
          </h4>
        </div>

        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table data={data?.results ?? []} columns={columns} />
            {data?.metadata && (
              <TableDataPagination
                isUseExternalState
                page={page}
                setPage={setPage}
                metadata={data.metadata}
              />
            )}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
