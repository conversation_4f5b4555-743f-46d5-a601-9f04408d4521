"use client";

import { Card } from "flowbite-react";
import { FiDatabase } from "react-icons/fi";

import { DonutChart, getDonutChartColor } from "@/components/ui/charts/donut";
import { Skeleton } from "@/components/ui/skeleton";
import { createRandomArray } from "@/lib/utils";
import { snakeCaseToCapitalized } from "@/utils/string";

import { useMethodDistribution } from "./hooks/use-overview-categorization";

export const CategorizationMethodDistribution = () => {
  const { data, isPending } = useMethodDistribution();

  const totalDocuments = data?.reduce((sum, method) => sum + method.count, 0);

  const formattedData = data?.map((method) => ({
    method: snakeCaseToCapitalized(method.method),
    count: method.count,
  }));

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        Categorization Method Distribution
      </h3>

      {isPending ? (
        <CategorizationMethodDistributionSkeleton />
      ) : (
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
          <Card className="xl:col-span-2 [&>div]:p-4 sm:[&>div]:p-6">
            <div className="mb-4 text-center">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Documents by Categorization Method
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total: {totalDocuments?.toLocaleString()} categorized documents
              </p>
            </div>
            <div className="h-96">
              {formattedData?.length ? (
                <DonutChart
                  data={formattedData}
                  dataKey="count"
                  nameKey="method"
                  totalLabel="methods"
                />
              ) : (
                <div className="grid h-full place-content-center gap-2 text-center text-gray-500 dark:text-gray-400">
                  <FiDatabase className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" />
                  <p className="mt-2 text-sm">No data available</p>
                </div>
              )}
            </div>
          </Card>

          <Card className="[&>div]:justify-start">
            <h4 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              Method Breakdown
            </h4>
            <div className="max-h-80 space-y-3 overflow-y-auto pr-2">
              {data?.map((method, index) => {
                return (
                  <div
                    key={method.method}
                    className="flex items-center justify-between"
                  >
                    <div className="flex min-w-0 items-center space-x-3">
                      <div
                        className="h-4 w-4 rounded-full"
                        style={{
                          backgroundColor: getDonutChartColor(index).light,
                        }}
                      />
                      <span className="truncate text-sm font-medium text-gray-900 dark:text-white">
                        {snakeCaseToCapitalized(method.method)}
                      </span>
                    </div>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">
                      {method.count.toLocaleString()}
                    </span>
                  </div>
                );
              })}
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

const CategorizationMethodDistributionSkeleton = () => (
  <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
    <Card className="xl:col-span-2">
      <div className="mb-4 text-center">
        <Skeleton className="mx-auto mb-2 h-6 w-48" />
        <Skeleton className="mx-auto h-4 w-52" />
      </div>
      <div className="flex h-80 items-center justify-center">
        <Skeleton className="size-48 rounded-full" />
      </div>
    </Card>

    <Card>
      <Skeleton className="mb-4 h-6 w-32" />
      <div className="max-h-80 space-y-3">
        {createRandomArray(5).map((_, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-5 w-28" />
            </div>
            <div className="flex flex-col items-end space-y-1">
              <Skeleton className="h-5 w-12" />
              <Skeleton className="h-4 w-8" />
            </div>
          </div>
        ))}
      </div>
    </Card>
  </div>
);
