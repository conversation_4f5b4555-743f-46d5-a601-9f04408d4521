"use client";

import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";

import { AutoCategorizationTableFilter } from "./auto-categorization-table-filter";
import { columns } from "./columns";
import { useAutoCategorization } from "./hooks/use-auto-categorization-queries";

const BREADCRUMB_ITEMS = [{ label: "Auto Categorization" }];

export const AutoCategorizationDetail = () => {
  const { data, isPending, isPlaceholderData } = useAutoCategorization();

  return (
    <div className="space-y-4">
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader
        showBackButton
        href="/dashboards?tab=analytics&subTab=categorization"
      >
        Auto Categorization
      </PageHeader>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-end p-4">
          <AutoCategorizationTableFilter />
        </div>

        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} enableSorting data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
