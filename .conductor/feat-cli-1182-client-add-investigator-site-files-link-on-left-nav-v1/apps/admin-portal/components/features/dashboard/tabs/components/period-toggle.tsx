import { Tooltip } from "flowbite-react";
import { CalendarDays, CalendarMinus2 } from "lucide-react";

import { cn } from "@/lib/utils";

const PERIODS = [
  {
    label: <CalendarDays size={24} />,
    value: "daily",
  },
  {
    label: <CalendarMinus2 size={24} />,
    value: "weekly",
  },
] as const;

type Props = {
  currentPeriod: "daily" | "weekly";
  setCurrentPeriod: (period: "daily" | "weekly") => void;
};

export const PeriodToggle = ({ currentPeriod, setCurrentPeriod }: Props) => {
  return (
    <div className="w-fit rounded-full bg-gray-200 p-1 marker:p-2 dark:bg-gray-700">
      <div className="relative grid grid-cols-2">
        {PERIODS.map((period) => (
          <Tooltip
            theme={{
              target: "z-10",
              content: "capitalize",
            }}
            key={period.value}
            content={period.value}
            placement="top"
          >
            <button
              className={cn(
                "z-10 grid place-content-center p-2 capitalize text-gray-400 transition-all",
                currentPeriod === period.value &&
                  "text-white dark:text-gray-100",
              )}
              onClick={() => setCurrentPeriod(period.value)}
            >
              {period.label}
            </button>
          </Tooltip>
        ))}
        <div
          className={cn(
            "absolute left-0 top-0 h-full w-1/2 rounded-full bg-blue-700 transition-all",
            currentPeriod === "daily" && "translate-x-0",
            currentPeriod === "weekly" && "translate-x-full",
          )}
        />
      </div>
    </div>
  );
};
