"use client";

import { Card } from "flowbite-react";
import { FiDatabase } from "react-icons/fi";

import { DonutChart, getDonutChartColor } from "@/components/ui/charts/donut";
import { Skeleton } from "@/components/ui/skeleton";
import { createRandomArray } from "@/lib/utils";

import { useRoleDistribution } from "./hooks/use-user-management-queries";

export const RoleDistribution = () => {
  const { data, isLoading } = useRoleDistribution();
  const totalProfiles = data?.reduce((sum, role) => sum + role.count, 0);

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        Role Distribution
      </h3>

      {isLoading ? (
        <RoleDistributionSkeleton />
      ) : (
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
          <Card className="xl:col-span-2 [&>div]:p-4 sm:[&>div]:p-6">
            <div className="mb-4 text-center">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Profiles by Role
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total: {totalProfiles} profiles assigned
              </p>
            </div>
            <div className="h-96">
              {data?.length ? (
                <DonutChart
                  data={data}
                  dataKey="count"
                  nameKey="roleName"
                  totalLabel="profiles"
                />
              ) : (
                <div className="grid h-full place-content-center gap-2 text-center text-gray-500 dark:text-gray-400">
                  <FiDatabase className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" />
                  <p className="mt-2 text-sm">No data available</p>
                </div>
              )}
            </div>
          </Card>

          <Card className="[&>div]:justify-start">
            <h4 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              Role Breakdown
            </h4>
            <div className="max-h-96 space-y-3 overflow-y-auto pr-2">
              {data?.map((role, index) => (
                <div
                  key={role.roleId}
                  className="flex items-center justify-between"
                >
                  <div className="flex min-w-0 items-center space-x-3">
                    <div
                      className="h-4 w-4 rounded-full"
                      style={{
                        backgroundColor: getDonutChartColor(index).light,
                      }}
                    />
                    <span className="truncate text-sm font-medium text-gray-900 dark:text-white">
                      {role.roleName}
                    </span>
                  </div>
                  <div className="text-sm font-bold text-gray-900 dark:text-white">
                    {role.count}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

const RoleDistributionSkeleton = () => (
  <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
    <Card className="xl:col-span-2">
      <div className="mb-4 text-center">
        <Skeleton className="mx-auto mb-2 h-6 w-32" />
        <Skeleton className="mx-auto h-4 w-40" />
      </div>
      <div className="flex h-80 items-center justify-center">
        <Skeleton className="size-48 rounded-full" />
      </div>
    </Card>

    <Card>
      <Skeleton className="mb-4 h-6 w-28" />
      <div className="max-h-80 space-y-3">
        {createRandomArray(9).map((_, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-5 w-32" />
            </div>
            <Skeleton className="mb-1 h-5 w-8" />
          </div>
        ))}
      </div>
    </Card>
  </div>
);
