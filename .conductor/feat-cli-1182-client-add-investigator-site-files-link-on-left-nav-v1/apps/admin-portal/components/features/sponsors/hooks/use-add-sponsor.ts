import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddSponsorPayload } from "@/lib/apis/sponsors/types";

import { USE_SPONSORS_QUERY_KEY } from "./use-sponsors";

export const useAddSponsor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddSponsorPayload) => api.sponsors.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USE_SPONSORS_QUERY_KEY] });
    },
    onError: (error) => {
      toast.dismiss();
      toast.error(error.message);
    },
  });
};
