import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import type { Study } from "@/lib/apis/studies";
import { formatDate } from "@/lib/utils";

export const columns: ColumnDef<Study>[] = [
  {
    header: "Name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer font-medium hover:underline"
        href={`/studies/${row.original.id}`}
      >
        {row.original.name}
      </Link>
    ),
  },
  {
    header: "Start Date",
    accessorFn: (row) => formatDate(row.startDate, "MMM d, yyyy"),
  },
];
