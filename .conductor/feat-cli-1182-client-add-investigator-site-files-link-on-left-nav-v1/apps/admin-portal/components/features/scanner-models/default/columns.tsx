import { type ColumnDef } from "@tanstack/react-table";
import { MdOutlineEdit } from "react-icons/md";

import type { ScannerModel } from "@/lib/apis/scanner-models";

export const getColumns = (
  onEdit: (model: ScannerModel) => void,
): ColumnDef<ScannerModel>[] => [
  {
    accessorKey: "companyName",
    header: "Company Name",
    cell: ({ row }) => (
      <div className="font-bold dark:font-medium">
        {row.getValue("companyName")}
      </div>
    ),
  },
  {
    accessorKey: "modelName",
    header: "Model Name",
    cell: ({ row }) => (
      <button
        onClick={() => onEdit(row.original)}
        className="text-primary-500 hover:underline"
      >
        {row.getValue("modelName")}
      </button>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const model = row.original;

      return (
        <div
          className="text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
          onClick={() => onEdit(model)}
        >
          <span className="whitespace-nowrap">Edit</span>
          <MdOutlineEdit />
        </div>
      );
    },
  },
];
