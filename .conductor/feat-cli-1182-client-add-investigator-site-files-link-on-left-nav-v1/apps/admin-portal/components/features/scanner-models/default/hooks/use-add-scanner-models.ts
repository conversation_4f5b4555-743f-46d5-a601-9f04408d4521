import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddScannerModelPayload } from "@/lib/apis/scanner-models";

import { USE_SCANNER_MODELS_QUERY_KEY } from "./use-scanner-models";

export const useAddScannerModels = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: AddScannerModelPayload) => {
      const { ...payload } = data;
      return api.scannerModels.create(payload);
    },
    onSuccess: () => {
      toast.success("Scanner Model added successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_SCANNER_MODELS_QUERY_KEY],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
