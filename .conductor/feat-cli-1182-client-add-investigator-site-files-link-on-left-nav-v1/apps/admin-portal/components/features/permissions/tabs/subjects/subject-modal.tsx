import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>on } from "@/components/ui/button";
import { Form, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { RoleSubject } from "@/lib/apis/roles";

import { useUpdatePermissionSubjects } from "./hooks/use-permission-subject-mutations";

const schema = z.object({
  description: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedSubject: RoleSubject;
};

export const SubjectModal = function ({
  isOpen,
  onClose,
  selectedSubject,
}: Props) {
  const { isPending, mutateAsync } = useUpdatePermissionSubjects();
  const onSubmit = async (data: z.infer<typeof schema>) => {
    await mutateAsync({ ...data, id: selectedSubject.id });
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Edit Permission Subject`}
    >
      <Form
        defaultValues={{
          description: selectedSubject?.description || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button type="submit" isLoading={isPending} variant="primary">
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
