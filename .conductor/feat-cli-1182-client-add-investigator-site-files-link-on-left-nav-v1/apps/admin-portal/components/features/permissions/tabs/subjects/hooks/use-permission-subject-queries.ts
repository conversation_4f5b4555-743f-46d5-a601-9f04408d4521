import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const permissionSubjectKeys = {
  all: () => ["permission-subjects"] as const,

  allList: () => [...permissionSubjectKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...permissionSubjectKeys.allList(), params] as const,
};

export const usePermissionSubjects = () => {
  const { search } = useSearch();
  const { page, take } = usePagination();
  const params = {
    page,
    take,
    filter: {
      subjectName: search,
    },
  };
  return useQuery({
    queryKey: permissionSubjectKeys.list(params),
    queryFn: () => api.roles.getSubjects(params),
    placeholderData: (prev) => prev,
  });
};
