import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Permission } from "@/lib/apis/roles";

import { useUpdatePermissionAction } from "./hooks/use-permission-actions-mutations";

const schema = z.object({
  description: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedAction: Permission;
};

export const ActionModal = function ({
  isOpen,
  onClose,
  selectedAction,
}: Props) {
  const { isPending, mutateAsync } = useUpdatePermissionAction();
  const onSubmit = async (data: z.infer<typeof schema>) => {
    await mutateAsync({ ...data, id: selectedAction.id });
    onClose();
  };

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title={`Edit Permission`}>
      <Form
        defaultValues={{
          description: selectedAction?.description || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button type="submit" isLoading={isPending} variant="primary">
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
