import { use<PERSON>ara<PERSON> } from "next/navigation";
import { useRef } from "react";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  FormActions,
  InputField,
  Textarea,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { CohortItem } from "@/lib/apis/cohorts";
import { cn } from "@/lib/utils";

import { useAddCohort, useUpdateCohort } from "./hooks/use-cohorts-mutations";

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean(),
});

type FormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedCohort: CohortItem | null;
};

export const ModalCohort = function ({
  isOpen,
  onClose,
  selectedCohort,
}: Props) {
  const formRef = useRef<FormActions<z.ZodType<FormValues>>>(null);
  const params = useParams();

  const { mutateAsync: addCohortMutate, isPending: isPendingAddCohort } =
    useAddCohort();

  const { mutateAsync: updateCohortMutate, isPending: isPendingUpdateCohort } =
    useUpdateCohort();

  const isEditing = !!selectedCohort;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateCohortMutate({
          ...data,
          studyId: params.id! as string,
          id: selectedCohort.id,
        })
      : await addCohortMutate({ ...data, studyId: params.id! as string });

    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>{isEditing ? "Edit" : "Add"} Cohort</Modal.Header>
      <Modal.Body>
        <Form
          defaultValues={{
            name: selectedCohort?.name || "",
            description: selectedCohort?.description || "",
            isActive:
              typeof selectedCohort?.isActive === "boolean"
                ? selectedCohort.isActive
                : true,
          }}
          ref={formRef}
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
        >
          <div className="grid grid-cols-1 gap-6">
            <div className="flex flex-col gap-2">
              <Label htmlFor="name">Name</Label>
              <InputField
                id="name"
                name="name"
                placeholder="Enter cohort name..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description..."
              />
            </div>
            <div
              className={cn("flex items-center gap-2", !isEditing && "hidden")}
            >
              <Checkbox id="isActive" name="isActive" />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              disabled={isPendingAddCohort || isPendingUpdateCohort}
              isLoading={isPendingAddCohort || isPendingUpdateCohort}
              variant="primary"
            >
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
