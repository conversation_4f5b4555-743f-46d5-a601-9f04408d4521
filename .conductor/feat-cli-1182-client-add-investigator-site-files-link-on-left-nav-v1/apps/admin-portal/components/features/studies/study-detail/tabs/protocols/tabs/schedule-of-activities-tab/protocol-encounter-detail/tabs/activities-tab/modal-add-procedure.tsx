import { useParams } from "next/navigation";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";

import { useCreateActivityProcedures } from "../../hooks/use-create-procedure";

export const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean().optional().default(true),
});

type ModalAddActivityProps = {
  activityId: string;
  isOpen: boolean;
  onClose: () => void;
  encounterId: string;
};

export const ModalAddProcedure = function ({
  activityId,
  isOpen,
  onClose,
  encounterId,
}: ModalAddActivityProps) {
  const studyId = useParams()?.id as string;
  const { mutateAsync: createProcedure, isPending: isCreatingProcedure } =
    useCreateActivityProcedures(activityId);
  async function onSubmit(data: z.infer<typeof schema>) {
    await createProcedure({ ...data, studyId, encounterId });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Add Procedure</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            isActive: true,
            name: "",
            description: "",
          }}
        >
          <ActivityForm />
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              disabled={isCreatingProcedure}
              isLoading={isCreatingProcedure}
            >
              Add Procedure
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export const ActivityForm = () => {
  return (
    <div className="space-y-3 sm:space-y-6">
      <div className="space-y-1">
        <Label htmlFor="name">Name</Label>
        <InputField id="name" name="name" placeholder="Enter name..." />
      </div>

      <div className="space-y-1">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          placeholder="Enter description..."
        />
      </div>

      {/* <div className="space-x-2">
        <Checkbox id="isActive" name="isActive" />
        <Label htmlFor="isActive">Active</Label>
      </div> */}
    </div>
  );
};

export const ActivityFormSkeleton = () => {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
      {/* Name Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Group ID Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Description Field - spans 2 columns */}
      <div className="space-y-1 sm:col-span-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-24 w-full" />
      </div>

      {/* Is Active Field */}
      <div className="col-span-1 flex items-center gap-2">
        <Skeleton className="h-5 w-5" /> {/* Checkbox */}
        <Skeleton className="h-5 w-28" /> {/* Label */}
      </div>
    </div>
  );
};
