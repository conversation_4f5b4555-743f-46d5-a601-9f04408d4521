import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical } from "lucide-react";

import { cn } from "@/lib/utils";

type Props = {
  id: string;
};

export const HorizontalLine = ({ id }: Props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: transform ? CSS.Translate.toString(transform) : undefined,
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "rounded-xl border border-gray-200 p-4",
        isDragging && "z-50 opacity-50",
      )}
    >
      <div className="flex items-center gap-5 rounded-xl border border-gray-200 px-2.5 py-5 shadow">
        <div className="flex flex-1 items-center gap-2.5">
          <button
            className="cursor-grab rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
            {...attributes}
            {...listeners}
          >
            <GripVertical />
          </button>
          <div className="bg-primary-500 h-0.5 flex-1"></div>
        </div>
        <div className="rounded-full bg-gray-200 px-3 py-1 text-sm text-gray-700 dark:bg-gray-600 dark:text-gray-300">
          Horizontal Line
        </div>
      </div>
    </div>
  );
};
