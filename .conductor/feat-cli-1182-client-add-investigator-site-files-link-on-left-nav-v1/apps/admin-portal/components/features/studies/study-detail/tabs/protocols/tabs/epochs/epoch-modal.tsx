import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { ProtocolEpoch } from "@/lib/apis/protocols";
import { cn } from "@/lib/utils";

import { useAddEpoch, useUpdateEpoch } from "./hooks/use-epochs-mutations";

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedEpoch: ProtocolEpoch | null;
  protocolId: string;
};

export const EpochModal = function ({
  isOpen,
  onClose,
  selectedEpoch,
  protocolId,
}: Props) {
  const { mutateAsync: addEpochMutate, isPending: isPendingAddEpoch } =
    useAddEpoch(protocolId);

  const { mutateAsync: updateEpochMutate, isPending: isPendingUpdateEpoch } =
    useUpdateEpoch(protocolId);

  const isEditing = !!selectedEpoch;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateEpochMutate({
          ...data,
          id: selectedEpoch.id,
          protocolId,
        })
      : await addEpochMutate(data);

    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "Add"} Epoch`}
    >
      <Form
        defaultValues={{
          name: selectedEpoch?.name || "",
          description: selectedEpoch?.description || "",
          isActive:
            typeof selectedEpoch?.isActive === "boolean"
              ? selectedEpoch.isActive
              : true,
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter epoch name..."
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
              rows={4}
            />
          </div>
          <div
            className={cn("flex items-center gap-2", !isEditing && "hidden")}
          >
            <Checkbox id="isActive" name="isActive" />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            disabled={isPendingAddEpoch || isPendingUpdateEpoch}
            isLoading={isPendingAddEpoch || isPendingUpdateEpoch}
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
