import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";

import api from "@/lib/apis";

export const USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY =
  "encounter-activities";

/**
 * Custom hook to fetch activities for a specific protocol encounter by protocol ID and encounter ID
 * @param id - Protocol ID
 * @param encounterId - Encounter ID
 * @returns Query result containing encounter activities data
 */
export const useEncounterActivities = (id?: string) => {
  return useQuery({
    queryKey: [USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY, id],
    queryFn: () => api.encounters.getEncounterActivities(id!),
    enabled: !!id,
  });
};

export const useInfiniteActivities = (search: string, initialPageSize = 50) => {
  const params = useParams();
  return useInfiniteQuery({
    queryKey: ["infinite-activities", search],
    queryFn: ({ pageParam = 1 }) =>
      api.activities.list(params?.id as string, {
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
