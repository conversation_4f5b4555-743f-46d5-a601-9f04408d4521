import { ColumnDef } from "@tanstack/react-table";
import { MdArchive, MdC<PERSON>ntC<PERSON>, MdPublish } from "react-icons/md";

import {
  TableEditButton,
  TableGenericButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { FormStudioStatusBadge } from "@/components/ui/badges/form-studio-status-badge";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { FieldLibraryItem } from "@/lib/apis/field-library/types";

export const FIELD_TYPES = [
  "Text",
  "TextArea",
  "Integer",
  "Decimal",
  "Date",
  "DateTime",
  "RadioButton",
  "Checkbox",
  "Dropdown",
  // "Autocomplete",
] as const;

type FieldLibraryActions = {
  onEdit: (field: FieldLibraryItem) => void;
  onCopy: (field: FieldLibraryItem) => void;
  onPublish: (field: FieldLibraryItem) => void;
  onArchive: (field: FieldLibraryItem) => void;
  onDelete: (field: FieldLibraryItem) => void;
};

export const generateFieldLibraryColumns = (
  actions: FieldLibraryActions,
): ColumnDef<FieldLibraryItem>[] => [
  {
    accessorKey: "displayLabel",
    header: "Field Label",
    cell: ({ row }) => (
      <button
        onClick={() => actions.onEdit(row.original)}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.original.displayLabel}
      </button>
    ),
  },
  {
    accessorKey: "fieldName",
    header: "Field Name",
    cell: ({ row }) => (
      <span className="font-mono text-sm">{row.original.fieldName}</span>
    ),
  },
  {
    accessorKey: "fieldType",
    header: "Field Type",
    cell: ({ row }) => (
      <PillBadge className="text-sm" variant="default">
        {row.original.fieldType}
      </PillBadge>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <FormStudioStatusBadge variant={row.original.status} />,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const field = row.original;
      const canPublish = field.status === "DRAFT";
      const canArchive = field.status !== "ARCHIVED";
      const canDelete = field.status === "DRAFT";

      return (
        <div className="flex items-center gap-2">
          <TableEditButton
            type="button"
            onClick={() => actions.onEdit(field)}
          />

          <TableGenericButton
            type="button"
            onClick={() => actions.onCopy(field)}
          >
            Copy
            <MdContentCopy className="h-4 w-4" />
          </TableGenericButton>

          {canPublish && (
            <TableGenericButton
              type="button"
              onClick={() => actions.onPublish(field)}
            >
              Publish
              <MdPublish className="h-4 w-4" />
            </TableGenericButton>
          )}

          {canArchive && (
            <TableGenericButton
              type="button"
              className="text-red-500 hover:text-red-600"
              onClick={() => actions.onArchive(field)}
            >
              Archive
              <MdArchive className="h-4 w-4" />
            </TableGenericButton>
          )}

          {canDelete && (
            <TableRemoveButton
              onClick={() => actions.onDelete(field)}
              label="Delete"
            />
          )}
        </div>
      );
    },
  },
];
