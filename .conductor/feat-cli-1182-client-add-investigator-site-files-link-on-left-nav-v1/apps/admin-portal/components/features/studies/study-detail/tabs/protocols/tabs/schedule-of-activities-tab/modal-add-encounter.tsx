import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteVisitTypesByStudy } from "@/hooks/queries/use-infinite-visit-types-by-study";
import { Protocol } from "@/lib/apis/protocols";

import { useAddProtocolEncounter } from "../../hooks/use-add-encounter";
import { useInfiniteEpochs } from "../epochs/hooks/use-epochs-queries";
import { useInfiniteStudyArms } from "../study-arms/hooks/use-study-arms-queries";

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  visitTypeId: z.string().min(1, "Visit type is required"),
  description: z.string().optional(),
  epochId: z.string().optional(),
  studyArmId: z.string().optional(),
  visitDay: z.coerce
    .number({
      invalid_type_error: "Visit day must be a number",
      required_error: "Visit day must be a number",
    })
    .optional(),
  visitWindowStart: z.coerce
    .number({
      invalid_type_error: "Window start must be a number",
      required_error: "Window start must be a number",
    })
    .max(0, "Maximum value is 0")
    .optional(),
  visitWindowEnd: z.coerce
    .number({
      invalid_type_error: "Window end must be a number",
      required_error: "Window end must be a number",
    })
    .min(0, "Minimum value is 0")
    .optional(),
});

type ModalAddEncounterProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedProtocol: Protocol | null;
};

export const ModalAddEncounter = function ({
  isOpen,
  onClose,
  selectedProtocol,
}: ModalAddEncounterProps) {
  const { mutateAsync: createVisit, isPending } = useAddProtocolEncounter();

  async function onSubmit(data: z.infer<typeof schema>) {
    await createVisit({
      protocolId: selectedProtocol?.id as string,
      ...data,
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-3xl">
      <Modal.Header>Add Visit</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          defaultValues={{
            name: "",
            visitTypeId: "",
            description: "",
            epochId: "",
            studyArmId: "",
            visitDay: "",
            visitWindowStart: "",
            visitWindowEnd: "",
          }}
          formProps={{ shouldFocusError: false }}
        >
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="name">Name</Label>
              <InputField id="name" name="name" placeholder="Enter name" />
            </div>

            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="visitDay">Visit Day</Label>
              <InputNumber
                id="visitDay"
                placeholder="0"
                name="visitDay"
                decimalScale={0}
                isShowButtons
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="visitWindowStart">Visit Window Start</Label>
              <InputNumber
                id="visitWindowStart"
                placeholder="0"
                name="visitWindowStart"
                max={0}
                decimalScale={0}
                isShowButtons
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="visitWindowEnd">Visit Window End</Label>
              <InputNumber
                id="visitWindowEnd"
                placeholder="0"
                name="visitWindowEnd"
                min={0}
                decimalScale={0}
                isShowButtons
              />
            </div>
            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="visitTypeId">Visit Type</Label>
              <LazySelect
                name="visitTypeId"
                id="visitTypeId"
                placeholder="Select visit type"
                searchPlaceholder="Search for visit types..."
                useInfiniteQuery={useInfiniteVisitTypesByStudy}
                getOptionLabel={(type) => type.name}
                getOptionValue={(type) => type.id}
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="studyArmId">Study Arm</Label>
              <LazySelect
                name="studyArmId"
                id="studyArmId"
                placeholder="Select study arm"
                searchPlaceholder="Search study arm..."
                useInfiniteQuery={useInfiniteStudyArms}
                getOptionLabel={(studyArm) => studyArm.name}
                getOptionValue={(studyArm) => studyArm.id}
                params={[selectedProtocol?.id]}
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="epochId">Epoch</Label>
              <LazySelect
                name="epochId"
                id="epochId"
                placeholder="Select epoch"
                searchPlaceholder="Search epoch..."
                useInfiniteQuery={useInfiniteEpochs}
                getOptionLabel={(epoch) => epoch.name}
                getOptionValue={(epoch) => epoch.id}
                params={[selectedProtocol?.id]}
              />
            </div>

            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description"
                className="resize-none"
                rows={4}
              />
            </div>
          </div>
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              disabled={isPending}
              isLoading={isPending}
            >
              Add Visit
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
