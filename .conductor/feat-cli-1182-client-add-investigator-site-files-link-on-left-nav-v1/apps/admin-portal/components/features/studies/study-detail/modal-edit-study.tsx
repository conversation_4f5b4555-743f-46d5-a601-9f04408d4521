import { useParams } from "next/navigation";
import type { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Modal } from "@/components/ui/modal";
import type { Study, UpdateStudyPayload } from "@/lib/apis/studies";

import { schema, StudyForm } from "../default/modal-add-study";
import { useUpdateStudy } from "../hooks/use-studies-mutations";

type ModalEditStudyProps = {
  isOpen: boolean;
  onClose: () => void;
  study?: Study;
};

const editStudySchema = schema.omit({
  interventionType: true,
  randomization: true,
  primaryEndpoint: true,
});

export const ModalEditStudy = function ({
  isOpen,
  onClose,
  study,
}: ModalEditStudyProps) {
  const { id } = useParams();
  const { mutateAsync: updateStudy, isPending } = useUpdateStudy();
  async function onSubmit(data: z.infer<typeof editStudySchema>) {
    await updateStudy({ data: data as UpdateStudyPayload, id: id as string });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-4xl">
      <Modal.Header>Edit Study</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={editStudySchema}
          onSubmit={onSubmit}
          defaultValues={{
            ...study,
            sponsorId: study?.sponsor?.id,
            numberOfQCSteps: study?.numberOfQCSteps || 1,
            artifactCategoryVersionId: study?.artifactCategoryVersionId || "",
          }}
        >
          <StudyForm isEditing />
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button type="submit" variant="primary" isLoading={isPending}>
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
