"use client";

import { useParams } from "next/navigation";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { useStudy } from "../hooks/use-studies-queries";
import { ActivitiesTab } from "./tabs/activities";
import { CohortsTab } from "./tabs/cohorts";
import { ContactsTab } from "./tabs/contacts";
import { FormStudioTab } from "./tabs/form-studio";
import { ProceduresTab } from "./tabs/procedures";
import { ProtocolContainerTab } from "./tabs/protocols";
import { SitesTab } from "./tabs/sites/sites-tab";
import { StudyDetailTab } from "./tabs/study-detail-tab";
import { UserTab } from "./tabs/users";
import { VisitTypesTab } from "./tabs/visit-types";

const STUDY_TABS = [
  {
    title: "Study Detail",
    key: "detail",
    content: <StudyDetailTab />,
  },
  {
    key: "users",
    content: <UserTab />,
  },
  {
    key: "sites",
    content: <SitesTab />,
  },
  {
    key: "cohorts",
    content: <CohortsTab />,
  },
  {
    key: "protocols",
    content: <ProtocolContainerTab />,
  },
  {
    key: "activities",
    content: <ActivitiesTab />,
  },
  {
    key: "procedures",
    content: <ProceduresTab />,
  },
  {
    key: "visit-types",
    content: <VisitTypesTab />,
    query: "isActive=true",
  },
  {
    key: "form-studio",
    title: "Form Studio",
    content: <FormStudioTab />,
  },
  {
    key: "contacts",
    content: <ContactsTab />,
  },
];

export const StudyDetailContent = () => {
  const params = useParams();
  const id = params.id as string;

  const { data: study, isLoading: isLoadingStudy } = useStudy(id);

  const breadcrumbItems = [
    { label: "Studies", href: "/studies" },
    { label: study?.name ?? "Study Detail", loading: isLoadingStudy },
  ];
  return (
    <>
      <div className="space-y-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <PageHeader showBackButton href="/studies">
          {study?.name ?? "Study Detail"}
        </PageHeader>
        <TabsWrapper tabs={STUDY_TABS} />
      </div>
    </>
  );
};
