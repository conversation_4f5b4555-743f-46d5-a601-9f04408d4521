import { useParams } from "next/navigation";
import React from "react";
import { z } from "zod";

import { Button, CloseButton } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { FileDropzone } from "@/components/ui/form/file-drop-zone";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";

import { useImportProcedures } from "./hooks/use-procedures-mutations";

const schema = z.object({
  file: z.instanceof(File, { message: "Please select a CSV file" }),
});
type Props = {
  isOpen: boolean;
  onClose: () => void;
};
export const ImportProceduresModal = ({ isOpen, onClose }: Props) => {
  const studyId = useParams().id as string;
  const { mutateAsync, isPending } = useImportProcedures(studyId);

  const handleSubmit = async (data: z.infer<typeof schema>) => {
    const formData = new FormData();
    formData.append("file", data.file);
    await mutateAsync({
      formData,
      id: studyId,
    });
    onClose();
  };

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title="Import Procedures">
      <Form schema={schema} onSubmit={handleSubmit} className="space-y-4">
        <div className="flex flex-col gap-2">
          <Label required htmlFor="file">
            Procedures
          </Label>
          <FileDropzone
            name="file"
            acceptTypes={[".csv"]}
            maxSizeMB={10}
            multiple={false}
          />
        </div>
        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button type="submit" variant="primary" isLoading={isPending}>
            Import
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
