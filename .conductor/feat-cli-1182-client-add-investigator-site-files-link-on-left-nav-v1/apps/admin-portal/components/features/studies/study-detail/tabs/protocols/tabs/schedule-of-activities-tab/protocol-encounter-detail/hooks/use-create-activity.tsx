import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { AddEncounterActivityPayload } from "@/lib/apis/encounters";

import { USE_PROTOCOL_ENCOUNTERS_QUERY_KEY } from "../../../../hooks/use-protocol-encounters";
import { USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY } from "./use-encounter-activities";
import { USE_PROTOCOL_ENCOUNTER_QUERY_KEY } from "./use-protocol-encounter";

export const useCreateEncounterActivity = (encounterId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddEncounterActivityPayload) =>
      api.encounters.createEncounterActivity(encounterId, data),
    onSuccess: () => {
      toast.success("Activity created successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY],
      });
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });
};

export const useCreateExistingEncounterActivity = (encounterId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      activityIds,
    }: {
      activityIds: string[];
      protocolId: string;
    }) =>
      api.encounters.createExistingEncounterActivity(encounterId, activityIds),
    onSuccess: (_, payload) => {
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTERS_QUERY_KEY, payload.protocolId],
      });
      queryClient.invalidateQueries({
        queryKey: [
          USE_PROTOCOL_ENCOUNTER_QUERY_KEY,
          payload.protocolId,
          encounterId,
        ],
      });
      return queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY],
      });
    },
    onSettled: (_, err) =>
      !err && toast.success("Activity created successfully"),
    onError: (err) => {
      toast.error(err.message);
    },
  });
};
