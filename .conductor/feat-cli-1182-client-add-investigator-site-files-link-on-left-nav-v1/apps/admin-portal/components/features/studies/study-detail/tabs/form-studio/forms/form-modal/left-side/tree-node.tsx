import { useDraggable, useDroppable } from "@dnd-kit/core";
import {
  ChevronDown,
  ChevronRight,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
} from "lucide-react";
import React, { useState } from "react";

import { useDisclosure } from "@/hooks/use-disclosure";
import { cn } from "@/lib/utils";

import { Question, QuestionFolder } from "./use-questions-queries";

export type TreeNodeProps = {
  folder: QuestionFolder;
  selectedFolderId: string | null;
  onFolderSelect: (folderId: string, path: string) => void;
  draggingFolder: QuestionFolder | null;
  draggingQuestion: Question | null;
  level: number;
  path: string;
  rootPath: string;
  indent?: number;
};

export type FolderWithPath = QuestionFolder & {
  path: string;
};

const DEFAULT_INDENT = 25;

export const TreeNode = ({
  draggingFolder,
  selectedFolderId,
  onFolderSelect,
  folder,
  level,
  path,
  rootPath,
  draggingQuestion,
  indent = DEFAULT_INDENT,
}: TreeNodeProps) => {
  const isCurrent = folder.id === selectedFolderId;

  const { isOpen: isOpenFolder, toggle: onToggleFolder } = useDisclosure();

  const {
    attributes,
    listeners,
    setNodeRef: setDragRef,
    isDragging: isDraggingNode,
  } = useDraggable({
    id: folder.id,
    data: { ...folder, path },
  });

  const { setNodeRef: setDropRef, isOver } = useDroppable({
    id: folder.id,
    data: { ...folder, path },
  });

  const isValidDropFolder =
    draggingFolder && isOver
      ? folder.id !== draggingFolder.id &&
        draggingFolder.parentDirectoryId !== folder.id
      : null;

  const isValidDropQuestion =
    draggingQuestion && isOver
      ? folder.id !== draggingQuestion?.folderId
      : null;

  const handleFolderClick = () => {
    onFolderSelect(folder.id, path);
    onToggleFolder();
  };

  return (
    <>
      <div
        ref={(node) => {
          setDragRef(node);
          setDropRef(node);
        }}
        className={cn(
          "group flex w-full cursor-pointer items-center justify-between whitespace-nowrap border border-transparent px-3 py-1.5 transition-colors hover:relative hover:border-purple-500",
          isCurrent && "bg-purple-50 text-purple-700 dark:bg-[#1a2b3c]",
          isDraggingNode && "opacity-50",
          typeof isValidDropFolder === "boolean"
            ? isValidDropFolder
              ? "border-dashed border-purple-500 bg-purple-50 dark:bg-[#1a2b3c]"
              : "border-dashed border-red-500 !bg-red-300/45"
            : "",
          typeof isValidDropQuestion === "boolean"
            ? isValidDropQuestion
              ? "border-dashed border-purple-500 bg-purple-50 dark:bg-[#1a2b3c]"
              : "border-dashed border-red-500 !bg-red-300/45"
            : "",
        )}
        data-path={path}
        style={{
          paddingLeft: level * indent,
        }}
        onClick={handleFolderClick}
        {...attributes}
        {...listeners}
      >
        <div className="flex min-w-0 flex-1 items-center justify-between gap-x-1 pl-4">
          <div className="flex min-w-0 flex-1 items-center gap-2.5">
            <div className="flex items-center">
              <div className="flex w-5 items-center">
                {!!folder.subfolders.length && (
                  <>
                    {isOpenFolder ? (
                      <ChevronDown
                        className={cn(
                          "size-4",
                          isCurrent
                            ? "text-purple-700"
                            : "text-black dark:text-white",
                        )}
                      />
                    ) : (
                      <ChevronRight
                        className={cn(
                          "size-4",
                          isCurrent
                            ? "text-purple-700"
                            : "text-black dark:text-white",
                        )}
                      />
                    )}
                  </>
                )}
              </div>

              <div className="flex w-5 items-center">
                {isOpenFolder ? (
                  <FolderOpenIcon
                    className={cn(
                      "h-5 w-5",
                      isCurrent
                        ? "text-purple-700"
                        : "text-black dark:text-white",
                    )}
                  />
                ) : (
                  <FolderIcon
                    className={cn(
                      "h-5 w-5",
                      isCurrent
                        ? "text-purple-700"
                        : "text-black dark:text-white",
                    )}
                  />
                )}
              </div>
            </div>

            <span className="flex-1 select-none truncate text-sm font-medium leading-5">
              {folder.name}
            </span>

            {typeof folder.questionCount === "number" && (
              <span
                className={cn(
                  "font-plus-jakarta ml-2 flex  select-none justify-center rounded-full px-2 py-0.5 text-center text-sm font-medium tracking-[0.28px]",
                  isCurrent
                    ? "bg-purple-500 text-white"
                    : "text-primary-500 bg-gray-300",
                )}
              >
                {folder.questionCount}
              </span>
            )}
          </div>
        </div>
      </div>

      {isOpenFolder &&
        !!folder.subfolders.length &&
        folder.subfolders.map((sub) => (
          <TreeNode
            key={sub.id}
            folder={sub}
            level={level + 1}
            draggingFolder={draggingFolder}
            draggingQuestion={draggingQuestion}
            path={`${path}/${sub.name}`}
            selectedFolderId={selectedFolderId}
            onFolderSelect={onFolderSelect}
            rootPath={rootPath}
            indent={indent}
          />
        ))}
    </>
  );
};
