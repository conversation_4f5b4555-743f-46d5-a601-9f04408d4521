import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

import { Variable } from "./columns";

const schema = z.object({
  label: z
    .string({
      required_error: "Label is required",
      invalid_type_error: "Label is required",
    })
    .min(1, "Label is required"),
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Name can only contain letters, numbers, and underscores",
    ),
  value: z
    .string({
      required_error: "Value is required",
      invalid_type_error: "Value is required",
    })
    .min(1, "Value is required"),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedVariable: Variable | null;
  onSave: (data: FormValues & { id?: string }) => void;
};

export const VariableModal = function ({
  isOpen,
  onClose,
  selectedVariable,
  onSave,
}: Props) {
  const isEditing = !!selectedVariable;

  const formMethods = useForm<FormValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      label: selectedVariable?.label || "",
      name: selectedVariable?.name || "",
      value: selectedVariable?.value || "",
      description: selectedVariable?.description || "",
    },
  });

  const isValid = formMethods.formState.isValid;

  const onSubmit = async (data: FormValues) => {
    const payload = isEditing ? { ...data, id: selectedVariable.id } : data;

    onSave(payload);
    onClose();
  };

  return (
    <Modal
      show={isOpen}
      theme={{
        body: {
          base: "h-full max-h-full max-w-full",
        },
        content: {
          base: "h-full max-h-full w-full !max-w-full p-4",
          inner: "h-full max-h-full max-w-full",
        },
      }}
    >
      <Modal.Body>
        <div className="h-full bg-white p-4 dark:bg-gray-900">
          <div className="flex h-full flex-col rounded border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between gap-2 border-b border-gray-200 p-5 dark:border-gray-700">
              <h3 className="text-2xl font-bold text-gray-900 lg:text-3xl dark:text-white">
                Variable Editor
              </h3>
              <button
                onClick={onClose}
                className="rounded-full p-2 text-gray-500 transition-colors hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex-1 overflow-y-auto p-5">
              <Form
                schema={schema}
                formMethods={formMethods}
                onSubmit={onSubmit}
                id="variable-form"
                className="space-y-5"
              >
                <div className="space-y-4 rounded-xl border border-gray-200 p-5 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-1">
                      <Label htmlFor="label">Label</Label>
                      <InputField
                        id="label"
                        name="label"
                        placeholder="Enter label..."
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="name">Name</Label>
                      <InputField
                        id="name"
                        name="name"
                        placeholder="Enter name..."
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="value">Value</Label>
                      <InputField
                        id="value"
                        name="value"
                        placeholder="Enter value..."
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      placeholder="Enter description..."
                    />
                  </div>
                </div>
              </Form>
            </div>
            <div className="flex flex-col justify-end gap-4 border-t border-gray-200 bg-gray-50 p-5 sm:flex-row sm:gap-5 dark:border-gray-700 dark:bg-gray-800">
              <CloseButton onClose={onClose} />
              <Button
                disabled={!isValid}
                type="submit"
                form="variable-form"
                variant="primary"
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
