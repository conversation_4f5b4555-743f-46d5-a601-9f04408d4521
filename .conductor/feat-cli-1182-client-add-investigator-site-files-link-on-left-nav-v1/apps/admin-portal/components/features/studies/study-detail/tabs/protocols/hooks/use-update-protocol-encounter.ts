import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateProtocolEncountersPayload } from "@/lib/apis/protocols";

import { USE_PROTOCOL_ENCOUNTER_QUERY_KEY } from "../tabs/schedule-of-activities-tab/protocol-encounter-detail/hooks/use-protocol-encounter";
import { USE_PROTOCOL_ENCOUNTERS_QUERY_KEY } from "./use-protocol-encounters";

export const useUpdateProtocolEncounter = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateProtocolEncountersPayload) =>
      api.protocols.updateProtocolsEncounter(payload),
    onSuccess: (_, { protocolId }) => {
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTER_QUERY_KEY, protocolId],
      });
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTERS_QUERY_KEY],
      });
    },
    onSettled: (_, err) => !err && toast.success("Visit updated successfully"),
    onError: (err) => toast.error(err?.message || "Failed to update visit"),
  });
};
