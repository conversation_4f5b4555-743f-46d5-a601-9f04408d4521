import { Card } from "flowbite-react/components/Card";
import { useParams } from "next/navigation";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { CohortItem } from "@/lib/apis/cohorts";

import { ModalCohort } from "./cohort-modal";
import { generateCohortColumns } from "./columns";
import { useCohorts } from "./hooks/use-cohorts-queries";

export const CohortsTab = () => {
  const params = useParams();

  const { data, isPending } = useCohorts(params?.id as string);

  const [isOpenCohortModal, setIsOpenCohortModal] = useState(false);

  const [selectedCohort, setSelectedCohort] = useState<CohortItem | null>(null);

  const onEdit = (item: CohortItem) => {
    setSelectedCohort(item);
    setIsOpenCohortModal(true);
  };

  const columns = generateCohortColumns({
    onEdit,
  });

  const onCloseCohortModal = () => {
    setIsOpenCohortModal(false);
    setSelectedCohort(null);
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Cohorts</div>
          <Button variant="primary" onClick={() => setIsOpenCohortModal(true)}>
            <IoMdAdd />
            Add Cohort
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>

      {isOpenCohortModal && (
        <ModalCohort
          selectedCohort={selectedCohort}
          isOpen={isOpenCohortModal}
          onClose={onCloseCohortModal}
        />
      )}
    </>
  );
};
