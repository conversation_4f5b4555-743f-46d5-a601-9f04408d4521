import { ColumnDef } from "@tanstack/react-table";

import { TableEditButton } from "@/components/shared/table-action-buttons";
import { FormStudioStatusBadge } from "@/components/ui/badges";
import { Question } from "@/lib/apis/questions/types";

export const generateQuestionColumns = (
  onEdit: (question: Question) => void,
): ColumnDef<Question>[] => [
  {
    accessorKey: "question",
    header: "Question",
    cell: ({ row }) => (
      <button
        onClick={() => onEdit(row.original)}
        className="text-primary-500 text-left hover:underline"
      >
        {row.original.questionName}
      </button>
    ),
  },
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "questionType",
    header: "Type",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <FormStudioStatusBadge variant={row.original.status} />,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <div className="flex gap-2">
        <TableEditButton type="button" onClick={() => onEdit(row.original)} />
      </div>
    ),
  },
];
