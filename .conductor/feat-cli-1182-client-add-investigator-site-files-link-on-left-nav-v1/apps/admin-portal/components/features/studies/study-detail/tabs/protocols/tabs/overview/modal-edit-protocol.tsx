import { Datepicker } from "@clincove/shared-ui";
import { useParams } from "next/navigation";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Checkbox, Form, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Protocol } from "@/lib/apis/protocols";

import { useUpdateProtocol } from "../../hooks/use-protocols-mutations";

export const PROTOCOL_STATUS = [
  "In Review",
  "Approved",
  "Active",
  "Expired",
] as const;

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  consentRequired: z.boolean().default(false),
  ammendmentDate: z.string().optional(),
  status: z.enum(PROTOCOL_STATUS, {
    errorMap: () => ({
      message: "Status is required",
    }),
  }),
});

type ModalEditProtocolProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedProtocol: Protocol | null;
  setSelectedProtocol: (protocol: Protocol | null) => void;
};

export const ModalEditProtocol = function ({
  isOpen,
  onClose,
  selectedProtocol,
  setSelectedProtocol,
}: ModalEditProtocolProps) {
  const params = useParams();
  const { mutateAsync: update, isPending } = useUpdateProtocol(
    params.id as string,
    selectedProtocol?.id,
  );

  async function onSubmit(data: z.infer<typeof schema>) {
    if (!selectedProtocol?.id) return;
    await update(
      { protocolId: selectedProtocol.id, data },
      {
        onSuccess: (data) => {
          setSelectedProtocol(data);
        },
      },
    );
    onClose();
  }

  const statusIndex = PROTOCOL_STATUS.findIndex(
    (status) => status === selectedProtocol?.status,
  );
  // only allow current/next/expired statuses
  const validStatusIndex = [
    statusIndex,
    statusIndex + 1,
    PROTOCOL_STATUS.length - 1,
  ];

  const validStatues = PROTOCOL_STATUS.filter((_, idx) =>
    validStatusIndex.includes(idx),
  );

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title="Edit Protocol">
      <Form
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={{
          name: selectedProtocol?.name,
          consentRequired: selectedProtocol?.consentRequired,
          ammendmentDate: selectedProtocol?.ammendmentDate,
          status: selectedProtocol?.status,
        }}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter protocol name..."
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="status">Status</Label>
            <Select
              id="status"
              name="status"
              placeholder="Select a status..."
              options={validStatues.map((s) => ({
                label: s,
                value: s,
              }))}
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="ammendmentDate">Amendment Date</Label>
            <Datepicker
              id="ammendmentDate"
              name="ammendmentDate"
              placeholder="Select date..."
              format="yyyy/mm/dd"
            />
          </div>

          <div className="flex items-center gap-2">
            <Checkbox id="consentRequired" name="consentRequired" />
            <Label htmlFor="consentRequired">Consent Required</Label>
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            disabled={isPending}
            isLoading={isPending}
            enabledForDirty
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
