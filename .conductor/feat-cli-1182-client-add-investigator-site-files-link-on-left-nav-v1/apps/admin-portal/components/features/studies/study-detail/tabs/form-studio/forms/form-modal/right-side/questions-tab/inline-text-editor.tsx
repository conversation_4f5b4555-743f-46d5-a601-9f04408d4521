import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical } from "lucide-react";
import { useState } from "react";

import { InlineEdit } from "@/components/ui/inline-edit";
import { cn } from "@/lib/utils";

type Props = {
  type: "header" | "text";
  id: string;
};

export const InlineTextEditor = ({ type, id }: Props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: transform ? CSS.Translate.toString(transform) : undefined,
    transition,
  };
  const [text, setText] = useState(`Sample ${type} content`);
  const isHeader = type === "header";

  const handleSave = async (value: string) => {
    setText(value);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "rounded-xl border border-gray-200 p-4",
        isDragging && "z-50 opacity-50",
      )}
    >
      <div className="flex items-center gap-5 rounded-xl border border-gray-200 px-2.5 py-5 shadow">
        <div className="flex flex-1 items-center gap-2.5">
          <button
            className="cursor-grab rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
            {...attributes}
            {...listeners}
          >
            <GripVertical />
          </button>
          <InlineEdit
            value={text}
            onSave={handleSave}
            placeholder={`Enter ${type} content...`}
            className="flex-1"
            viewModeClassName={cn(
              "hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md",
              isHeader ? "text-xl font-bold" : "text-sm font-medium",
            )}
            inputClassName={cn(
              isHeader ? "text-xl font-bold" : "text-sm font-medium",
            )}
          />
        </div>
        <div className="rounded-full bg-gray-200 px-3 py-1 text-sm capitalize text-gray-700 dark:bg-gray-600 dark:text-gray-300">
          {type}
        </div>
      </div>
    </div>
  );
};
