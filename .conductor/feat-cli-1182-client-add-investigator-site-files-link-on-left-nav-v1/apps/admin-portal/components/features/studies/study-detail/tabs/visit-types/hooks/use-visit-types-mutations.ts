import { useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type {
  CreateVisitTypePayload,
  UpdateVisitTypePayload,
} from "@/lib/apis/visit-types";

import { studyVisitTypeKeys } from "./use-visit-types-queries";

export const useAddStudyVisitType = () => {
  const params = useParams();
  const studyId = params.id as string;

  return useMutation({
    mutationFn: async (data: Omit<CreateVisitTypePayload, "studyId">) => {
      return api.visitTypes.create({ ...data, studyId });
    },
    onSettled: (_, err) =>
      !err && toast.success("Visit type added successfully"),
    onError: (error) =>
      toast.error(error.message || "Failed to add visit type"),
    meta: {
      awaits: studyVisitTypeKeys.allList(studyId),
    },
  });
};

export const useEditStudyVisitType = () => {
  const params = useParams();
  const studyId = params.id as string;

  return useMutation({
    mutationFn: async (data: UpdateVisitTypePayload & { id: string }) => {
      const { id, ...payload } = data;
      return api.visitTypes.update(id, { ...payload, studyId });
    },
    onSettled: (_, err) =>
      !err && toast.success("Visit type updated successfully"),
    onError: (error) =>
      toast.error(error.message || "Failed to update visit type"),
    meta: {
      awaits: studyVisitTypeKeys.allList(studyId),
    },
  });
};
