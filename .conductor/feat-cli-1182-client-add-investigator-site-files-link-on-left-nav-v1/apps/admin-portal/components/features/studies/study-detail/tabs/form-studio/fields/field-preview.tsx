import { Checkbox } from "flowbite-react";
import { X } from "lucide-react";
import { z } from "zod";

import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { DateTimePicker } from "@/components/ui/form/date-picker/date-time-picker";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { Radio } from "@/components/ui/form/radio";
import { Skeleton } from "@/components/ui/skeleton";
import { CodelistTerm } from "@/lib/apis/codelist-definitions/types";

import { useCodelistItem } from "../codelist/hooks/use-codelist-queries";
import { CODE_LIST_FIELDS, type FieldValues } from "./field-modal";

type Props = {
  onClose: () => void;
  values: FieldValues;
};

const createValidationSchema = (values: FieldValues) => {
  const { name, type, config, label } = values;

  if (!name) return z.object({});

  switch (type) {
    case "Text":
    case "TextArea":
      return z.object({
        [name]: config?.maxLength
          ? z
              .string({
                invalid_type_error: `${label} is required`,
                required_error: `${label} is required`,
              })
              .max(
                config.maxLength,
                `Maximum ${config.maxLength} characters allowed`,
              )
              .min(1, `${label} is required`)
          : z
              .string({
                invalid_type_error: `${label} is required`,
                required_error: `${label} is required`,
              })
              .min(1, `${label} is required`),
      });

    case "Integer":
    case "Decimal": {
      let numberSchema = z.number({
        invalid_type_error: `${label} is required`,
        required_error: `${label} is required`,
      });
      if (typeof config?.min === "number") {
        numberSchema = numberSchema.min(
          config.min,
          `Minimum value is ${config.min}`,
        );
      }
      if (typeof config?.max === "number") {
        numberSchema = numberSchema.max(
          config.max,
          `Maximum value is ${config.max}`,
        );
      }
      return z.object({
        [name]: numberSchema,
      });
    }

    case "Date":
    case "DateTime":
      return z.object({
        [name]: z.date({
          invalid_type_error: `${label} is required`,
          required_error: `${label} is required`,
        }),
      });

    case "Dropdown":
    case "RadioButton":
      return z.object({
        [name]: z
          .string({
            invalid_type_error: `${label} is required`,
            required_error: `${label} is required`,
          })
          .min(1, `${label} is required`),
      });

    case "Checkbox":
      return z.object({
        [name]: z.array(z.string()).min(1, "Please select at least one option"),
      });

    default:
      return z.object({
        [name]: z
          .string({
            invalid_type_error: `${label} is required`,
            required_error: `${label} is required`,
          })
          .min(1, `${label} is required`),
      });
  }
};
const renderField = ({
  values,
  isLoading,
  terms,
}: {
  values: FieldValues;
  terms?: CodelistTerm[];
  isLoading?: boolean;
}) => {
  const { name, label, type, config } = values;

  if (!name || !label || !type) {
    return (
      <div className="py-8 text-center text-gray-500">
        Complete field configuration to see preview
      </div>
    );
  }

  switch (type) {
    case "Text":
      return (
        <InputField
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
          maxLength={config?.maxLength}
        />
      );

    case "TextArea":
      return (
        <Textarea
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
          maxLength={config?.maxLength}
        />
      );

    case "Integer":
    case "Decimal":
      return (
        <InputNumber
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
          min={config?.min}
          max={config?.max}
          decimalScale={type === "Decimal" ? config?.decimalPlaces : 0}
          step={type === "Integer" ? 1 : 0.1}
        />
      );

    case "Date":
    case "DateTime":
      return (
        <DateTimePicker
          name={name}
          placeholder={`Select ${label.toLowerCase()}...`}
          format={
            config?.dateFormat as "mm/dd/yyyy" | "dd/mm/yyyy" | "yyyy/mm/dd"
          }
          maxDate={config?.disableFutureDates ? new Date() : undefined}
          minDate={config?.disablePastDates ? new Date() : undefined}
          showTime={type === "DateTime"}
        />
      );

    case "Dropdown":
      return (
        <Select
          name={name}
          options={terms?.map((term) => ({
            label: term.displayLabel,
            value: term.submissionValue,
          }))}
          placeholder={`Select ${label.toLowerCase()}...`}
        />
      );

    case "RadioButton":
      return (
        <div
          className={`flex gap-4 ${config?.layoutDirection === "vertical" ? "flex-col" : "flex-row"}`}
        >
          {isLoading
            ? [1, 2, 3].map((i) => <RadioSkeleton key={i} />)
            : terms?.map((term) => (
                <div key={term.id} className="flex items-center gap-2">
                  <Radio
                    name={name}
                    value={term.submissionValue}
                    id={`${name}-${term.submissionValue}`}
                  />
                  <Label htmlFor={`${name}-${term.submissionValue}`}>
                    {term.displayLabel}
                  </Label>
                </div>
              ))}
        </div>
      );

    case "Checkbox":
      return (
        <div
          className={`flex flex-wrap gap-4 ${config?.layoutDirection === "vertical" ? "flex-col" : "flex-row"}`}
        >
          {isLoading
            ? [1, 2, 3].map((i) => <CheckboxSkeleton key={i} />)
            : terms?.map((term) => (
                <div
                  key={term.submissionValue}
                  className="flex items-center gap-2"
                >
                  <Checkbox
                    name={`${name}.${term.submissionValue}`}
                    value={term.submissionValue}
                    id={`${name}-${term.submissionValue}`}
                  />
                  <Label htmlFor={`${name}-${term.submissionValue}`}>
                    {term.displayLabel}
                  </Label>
                </div>
              ))}
        </div>
      );

    default:
      return (
        <InputField
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
        />
      );
  }
};

const FieldPreview = ({ onClose, values }: Props) => {
  const { name, label, description, type, config } = values;

  const previewSchema = createValidationSchema(values);
  const { data, isPending } = useCodelistItem(
    CODE_LIST_FIELDS.includes(type) ? config?.codeListId || "" : "",
  );

  return (
    <div className="flex h-full flex-col rounded-xl border border-blue-500 bg-white shadow dark:border-blue-400 dark:bg-gray-900">
      <div className="flex items-center justify-between rounded-tl-xl rounded-tr-xl border-b border-b-blue-500 bg-blue-50 p-5 dark:border-b-blue-400 dark:bg-blue-900/20">
        <span className="text-xl font-bold text-gray-900 dark:text-white">
          Preview Mode
        </span>
        <button
          onClick={onClose}
          className="rounded-full bg-blue-500 p-2 text-white transition-colors hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="flex-1 space-y-2.5 overflow-y-auto rounded-bl-xl rounded-br-xl bg-white p-5 dark:bg-gray-900">
        <Form
          onSubmit={(data) => {
            //
          }}
          mode="all"
          defaultValues={{
            [values.name]:
              type === "Date" || type === "DateTime" ? undefined : "",
          }}
          schema={previewSchema}
        >
          {name && label && type ? (
            <div className="space-y-2">
              <Label htmlFor={name}>{label}</Label>
              {description && values.config?.isDisplayOnForm && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {description}
                </p>
              )}
              {config?.unitOfMeasure && (
                <p className="text-sm text-gray-500">
                  Unit: {config.unitOfMeasure}
                </p>
              )}
              {renderField({
                values,
                terms: data?.terms,
                isLoading: isPending,
              })}
            </div>
          ) : (
            renderField({
              values,
            })
          )}
        </Form>
      </div>
    </div>
  );
};

export default FieldPreview;

const CheckboxSkeleton = () => (
  <div className="flex items-center gap-2">
    <Skeleton className="size-4" />
    <Skeleton className="h-4 w-20" />
  </div>
);

const RadioSkeleton = () => (
  <div className="flex items-center gap-2">
    <Skeleton className="size-4 rounded-full" />
    <Skeleton className="h-4 w-20" />
  </div>
);
