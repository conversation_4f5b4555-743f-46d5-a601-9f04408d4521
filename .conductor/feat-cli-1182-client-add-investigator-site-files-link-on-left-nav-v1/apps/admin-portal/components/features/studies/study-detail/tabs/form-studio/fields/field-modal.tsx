import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { Toolt<PERSON> } from "flowbite-react";
import { debounce } from "lodash";
import { Edit, Eye, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z, ZodIssueCode } from "zod";

import { Button, CloseButton } from "@/components/ui/button";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Switch } from "@/components/ui/form/toggle-switch";
import { Modal } from "@/components/ui/modal";
import { FieldLibraryItem } from "@/lib/apis/field-library/types";
import { cn } from "@/lib/utils";

import { FIELD_TYPES } from "./columns";
import FieldPreview from "./field-preview";
import {
  useCreateField,
  useUpdateField,
} from "./hooks/use-field-library-mutations";
import {
  ChoiceValidations,
  DateValidations,
  NumberValidations,
  TextValidations,
} from "./validation-fields";

export const CODE_LIST_FIELDS = ["RadioButton", "Checkbox", "Dropdown"];

const getConfigByFieldType = (type: string, config: FieldValues["config"]) => {
  const baseConfig: Record<string, any> = {};

  switch (type) {
    case "Text":
    case "TextArea":
      if (config?.maxLength && typeof config.maxLength === "number") {
        baseConfig.maxLength = config.maxLength;
      }
      break;

    case "Integer":
    case "Decimal":
      if (typeof config?.min === "number") {
        baseConfig.min = config.min;
      }
      if (typeof config?.max === "number") {
        baseConfig.max = config.max;
      }
      if (config?.unitOfMeasure) {
        baseConfig.unitOfMeasure = config.unitOfMeasure;
      }
      if (type === "Decimal" && typeof config?.decimalPlaces === "number") {
        baseConfig.decimalPlaces = config.decimalPlaces;
      }
      break;

    case "Date":
    case "DateTime":
      if (config?.dateFormat) {
        baseConfig.dateFormat = config.dateFormat;
      }
      if (config?.disablePastDates) {
        baseConfig.disablePastDates = config.disablePastDates;
      }
      if (config?.disableFutureDates) {
        baseConfig.disableFutureDates = config.disableFutureDates;
      }
      break;

    case "RadioButton":
    case "Checkbox":
    case "Dropdown":
      if (config?.codeListId) {
        baseConfig.codeListId = config.codeListId;
      }
      if (
        (type === "RadioButton" || type === "Checkbox") &&
        config?.layoutDirection
      ) {
        baseConfig.layoutDirection = config.layoutDirection;
      }
      break;
  }

  // Common config for all types
  if (config?.isDisplayOnForm) {
    baseConfig.isDisplayOnForm = config.isDisplayOnForm;
  }

  return Object.keys(baseConfig).length > 0 ? baseConfig : undefined;
};
const baseSchema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Name can only contain letters, numbers, and underscores",
    ),
  label: z
    .string({
      required_error: "Label is required",
      invalid_type_error: "Label is required",
    })
    .min(1, "Label is required"),
  description: z.string().optional(),
  type: z.enum(FIELD_TYPES, {
    errorMap: () => ({ message: "Type is required" }),
  }),
  config: z
    .object({
      maxLength: z.coerce
        .number({ invalid_type_error: "Max length must be a number" })
        .optional(),
      min: z.coerce
        .number({ invalid_type_error: "Min value must be a number" })
        .optional(),
      max: z.coerce
        .number({ invalid_type_error: "Max value must be a number" })
        .optional(),
      unitOfMeasure: z.string().optional(),
      dateFormat: z.string().optional(),
      disableFutureDates: z.boolean().optional(),
      disablePastDates: z.boolean().optional(),
      layoutDirection: z
        .enum(["horizontal", "vertical"])
        .default("horizontal")
        .optional(),
      decimalPlaces: z.coerce
        .number({ invalid_type_error: "Decimal places must be a number" })
        .min(0, "Decimal places must be at least 0")
        .max(10, "Decimal places must be at most 10")
        .optional(),
      codeListId: z.string().optional(),
      isDisplayOnForm: z.boolean().optional(),
    })
    .optional(),
  // use this to run preprocess
  shortCode: z.string().optional(),
  isEditing: z.boolean().optional(),
});
const schema = z.preprocess((input, ctx) => {
  const configFields = baseSchema
    .pick({
      config: true,
      type: true,
    })
    .safeParse(input);

  if (configFields.success) {
    const configData = configFields.data;
    if (
      configData.type &&
      CODE_LIST_FIELDS.includes(configData.type) &&
      !configData.config?.codeListId
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "CodeList is required",
        path: ["config.codeListId"],
      });
    }

    if (
      (configData.type === "Date" || configData.type === "DateTime") &&
      !configData.config?.dateFormat
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Date format is required",
        path: ["config.dateFormat"],
      });
    }

    if (configData.type === "Integer" || configData.type === "Decimal") {
      const minValue = configData.config?.min;
      const maxValue = configData.config?.max;
      if (typeof minValue === "number" && typeof maxValue === "number") {
        if (minValue > maxValue) {
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: "Min value cannot be greater than max value",
            path: ["config.min"],
          });
        }
        if (maxValue < minValue) {
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: "Max value cannot be less than min value",
            path: ["config.max"],
          });
        }
      }
    }
  }

  const shortCodeFields = baseSchema
    .pick({
      shortCode: true,
      isEditing: true,
    })
    .safeParse(input);

  if (shortCodeFields.success) {
    const shortCodeFieldsData = shortCodeFields.data;
    if (shortCodeFieldsData.isEditing && !shortCodeFieldsData.shortCode) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Field ID is required",
        path: ["shortCode"],
      });
    }
  }

  return input;
}, baseSchema);

export type FieldValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedField: FieldLibraryItem | null;
};

const DISABLED_STATUSES = ["PUBLISHED", "ARCHIVED"];

export const FieldModal = function ({ isOpen, onClose, selectedField }: Props) {
  const [isPreview, setIsPreview] = useState(false);
  const { mutateAsync: createField, isPending: isCreatingField } =
    useCreateField();
  const { mutateAsync: updateField, isPending: isUpdatingField } =
    useUpdateField();

  const isEditing = !!selectedField;
  const isDisabled =
    DISABLED_STATUSES.includes(selectedField?.status ?? "") && isEditing;
  const isSubmitting = isCreatingField || isUpdatingField;

  const formMethods = useForm<FieldValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      name: selectedField?.fieldName || "",
      label: selectedField?.displayLabel || "",
      description: selectedField?.description || "",
      type: selectedField?.fieldType,
      shortCode: selectedField?.shortCode || "",
      config: {
        ...selectedField?.config,
      },
      isEditing: isEditing,
    },
    disabled: isDisabled || undefined,
  });
  const values = formMethods.getValues();

  const currentType = formMethods.watch("type");
  const isValid = formMethods.formState.isValid;

  const validationFieldsMap = {
    Text: <TextValidations key={0} />,
    TextArea: <TextValidations key={1} />,
    Integer: <NumberValidations key={0} type="integer" />,
    Decimal: <NumberValidations key={1} type="decimal" />,
    Date: <DateValidations key={0} type="Date" />,
    DateTime: <DateValidations key={1} type="DateTime" />,
    RadioButton: <ChoiceValidations key={0} />,
    Checkbox: <ChoiceValidations key={1} />,
    Dropdown: <ChoiceValidations key={2} showLayoutField={false} />,
  };

  const onSubmit = async (data: FieldValues) => {
    const config = getConfigByFieldType(data.type, data.config);

    const payload = {
      fieldName: data.name,
      fieldType: data.type,
      displayLabel: data.label,
      description: data.description || undefined,
      shortCode: data.shortCode || undefined,
      config,
    };

    if (isEditing && selectedField) {
      await updateField({ id: selectedField.id, ...payload });
    } else {
      await createField(payload);
    }

    onClose();
  };
  const handlePreview = () => {
    setIsPreview(!isPreview);
  };
  const handleClosePreview = () => {
    setIsPreview(false);
  };

  useEffect(() => {
    const { unsubscribe } = formMethods.watch((values, { name }) => {
      if (name !== "config.max" && name !== "config.min") return;
      formMethods.trigger(["config.min", "config.max"]);
    });
    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Modal
        show={isOpen}
        theme={{
          body: {
            base: "h-full max-h-full max-w-full",
          },
          content: {
            base: "h-full max-h-full w-full !max-w-full p-4",
            inner: "h-full max-h-full max-w-full",
          },
        }}
      >
        <Modal.Body>
          <div className="h-full bg-white p-4 dark:bg-gray-900">
            <div className="flex h-full flex-col rounded border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between gap-2 border-b border-gray-200 p-5 dark:border-gray-700">
                <h3 className="text-2xl font-bold text-gray-900 lg:text-3xl dark:text-white">
                  Field Editor
                </h3>
                <button
                  onClick={onClose}
                  className="rounded-full p-2 text-gray-500 transition-colors hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="flex-1 overflow-y-auto p-5">
                {isPreview && (
                  <FieldPreview onClose={handleClosePreview} values={values} />
                )}
                <Form
                  schema={schema}
                  formMethods={formMethods}
                  onSubmit={onSubmit}
                  id="fields-form"
                  className={cn("space-y-5", isPreview && "hidden")}
                >
                  <div className="space-y-4 rounded-xl border border-gray-200 p-5 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <div
                      className={cn(
                        "grid gap-4",
                        isEditing ? "grid-cols-4" : "grid-cols-3",
                      )}
                    >
                      <div className="space-y-1">
                        <Label htmlFor="type">Field Type</Label>
                        <Select
                          name="type"
                          options={FIELD_TYPES.map((type) => ({
                            label: type,
                            value: type,
                          }))}
                          placeholder="Select field type"
                          onChange={async (value) => {
                            if (value === currentType) return;
                            formMethods.setValue(
                              "config",
                              {
                                layoutDirection: "horizontal",
                              },
                              {
                                shouldValidate: true,
                              },
                            );
                          }}
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="name">Field Name</Label>
                        <InputField
                          id="name"
                          name="name"
                          placeholder="Enter field name..."
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="label">Field Label</Label>
                        <InputField
                          id="label"
                          name="label"
                          placeholder="Enter field label..."
                        />
                      </div>
                      {isEditing && (
                        <div className="space-y-1">
                          <Label required htmlFor="shortCode">
                            Short Code
                          </Label>
                          <InputField
                            id="shortCode"
                            name="shortCode"
                            placeholder="Enter field Id..."
                          />
                        </div>
                      )}
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        placeholder="Enter description..."
                      />
                      <div className="flex justify-end gap-2 py-2.5">
                        <Label htmlFor="config.isDisplayOnForm">
                          Display on Form
                        </Label>
                        <Switch sizing="sm" name="config.isDisplayOnForm" />
                      </div>
                    </div>
                  </div>

                  {!!currentType &&
                    !!validationFieldsMap[
                      currentType as keyof typeof validationFieldsMap
                    ] && (
                      <div className="space-y-2.5 rounded-xl border border-gray-200 p-5 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                        <span className="text-xl font-bold text-gray-900 dark:text-white">
                          Format
                        </span>

                        {currentType &&
                          validationFieldsMap[
                            currentType as keyof typeof validationFieldsMap
                          ]}
                      </div>
                    )}
                </Form>
              </div>
              <div
                className={cn(
                  "flex flex-col justify-between gap-4 border-t border-gray-200 bg-gray-50 p-5 sm:flex-row sm:gap-5 dark:border-gray-700 dark:bg-gray-800",
                )}
              >
                {isValid || isDisabled ? (
                  <Tooltip content={isPreview ? "Edit" : "Preview"}>
                    <Button
                      onClick={handlePreview}
                      className="!p-2.5 [&_span]:!p-0"
                      variant="outline"
                    >
                      {isPreview ? <Edit size={24} /> : <Eye size={24} />}
                    </Button>
                  </Tooltip>
                ) : (
                  <Tooltip content="Complete required fields to preview">
                    <Button
                      onClick={handlePreview}
                      className="!p-2.5 [&_span]:!p-0"
                      variant="outline"
                      disabled
                    >
                      <Eye size={24} />
                    </Button>
                  </Tooltip>
                )}
                <div className="flex flex-col gap-4 sm:flex-row sm:gap-5">
                  <CloseButton onClose={onClose} />
                  <Button
                    disabled={!isValid || isDisabled}
                    type="submit"
                    form="fields-form"
                    variant="primary"
                    isLoading={isSubmitting}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};
