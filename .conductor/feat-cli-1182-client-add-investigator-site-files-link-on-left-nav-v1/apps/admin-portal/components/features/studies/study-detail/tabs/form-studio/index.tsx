"use client";

import {
  <PERSON><PERSON><PERSON>elp,
  ChevronsLeftRight,
  Copy,
  Files,
  Variable,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";

import { CodelistTab } from "./codelist/codelist-tab";
import { FieldsTab } from "./fields/fields-tab";
import { FormsTab } from "./forms/forms-tab";
import { QuestionsTab } from "./questions/questions-tab";
import { VariablesTab } from "./variables/variables-tab";

const FORM_STUDIO_TABS = [
  {
    key: "forms",
    icon: <Files size={20} />,
    content: <FormsTab />,
  },
  {
    key: "questions",
    icon: <BadgeHelp size={20} />,
    content: <QuestionsTab />,
  },
  {
    key: "fields",
    icon: <Copy size={20} />,
    content: <FieldsTab />,
  },
  {
    key: "codelist",
    icon: <ChevronsLeftRight size={20} />,
    content: <CodelistTab />,
  },
  {
    key: "variables",
    icon: <Variable size={20} />,
    content: <VariablesTab />,
  },
] as const;

export const FormStudioTab = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab] = useQueryState(
    "subTab",
    parseAsString.withDefault(FORM_STUDIO_TABS[0].key),
  );

  const handleTabChange = (tabKey: string) => {
    const currentParams = new URLSearchParams(searchParams.toString());
    const preservedParams = new URLSearchParams();
    if (currentParams.get("tab")) {
      preservedParams.set("tab", currentParams.get("tab")!);
    }
    preservedParams.set("subTab", tabKey);

    router.replace(`?${preservedParams.toString()}`, { scroll: false });
  };

  const activeContent =
    FORM_STUDIO_TABS.find((tab) => tab.key === activeTab)?.content ??
    FORM_STUDIO_TABS[0].content;

  return (
    <div className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
      <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Form Studio
        </h2>
      </div>
      <div className="flex space-x-4 p-4">
        {FORM_STUDIO_TABS.map((tab) => (
          <button
            key={tab.key}
            onClick={() => handleTabChange(tab.key)}
            className={`relative flex items-center space-x-2 rounded-lg px-4 py-2 text-sm font-medium transition-all ${
              activeTab === tab.key
                ? "border-2 border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-600 dark:bg-blue-900/30 dark:text-blue-300"
                : "border-2 border-transparent text-gray-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700"
            }`}
          >
            {tab.icon}
            <span className="capitalize">{tab.key}</span>
          </button>
        ))}
      </div>
      <div className="rounded-lg bg-gray-50 dark:bg-gray-700">
        {activeContent}
      </div>
    </div>
  );
};
