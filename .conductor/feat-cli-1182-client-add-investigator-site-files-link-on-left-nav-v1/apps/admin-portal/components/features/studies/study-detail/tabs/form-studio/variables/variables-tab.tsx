"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { But<PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";

import { generateVariableColumns, Variable } from "./columns";
import { VariableModal } from "./variable-modal";

export const VariablesTab = () => {
  const {
    isOpen: isModalOpen,
    open: openModal,
    close: closeModal,
  } = useDisclosure();
  const [selectedVariable, setSelectedVariable] = useState<Variable | null>(
    null,
  );
  const [variables, setVariables] = useState<Variable[]>([
    {
      id: "1",
      label: "Patient Age",
      name: "age",
      description: "Patient age at screening",
      value: "65",
      status: "DRAFT",
      createdDate: "2024-01-15T10:30:00Z",
      lastUpdatedDate: "2024-01-15T10:30:00Z",
    },
    {
      id: "2",
      label: "Patient Gender",
      name: "gender",
      description: "Patient gender",
      value: "Female",
      status: "PUBLISHED",
      createdDate: "2024-01-15T10:35:00Z",
      lastUpdatedDate: "2024-01-15T10:35:00Z",
    },
    {
      id: "3",
      label: "Baseline Weight",
      name: "baseline_weight",
      description: "Patient weight at baseline visit",
      value: "72.5",
      status: "DRAFT",
      createdDate: "2024-01-15T11:00:00Z",
      lastUpdatedDate: "2024-01-15T11:00:00Z",
    },
    {
      id: "4",
      label: "Study Duration",
      name: "study_duration",
      description: "Total study duration in weeks",
      value: "52",
      status: "PUBLISHED",
      createdDate: "2024-01-15T11:15:00Z",
      lastUpdatedDate: "2024-01-15T11:15:00Z",
    },
    {
      id: "5",
      label: "Primary Diagnosis",
      name: "primary_diagnosis",
      description: "Primary diagnosis for study inclusion",
      value: "Type 2 Diabetes Mellitus",
      status: "ARCHIVED",
      createdDate: "2024-01-15T11:30:00Z",
      lastUpdatedDate: "2024-01-15T11:30:00Z",
    },
    {
      id: "6",
      label: "Baseline HbA1c",
      name: "baseline_hba1c",
      description: "Hemoglobin A1c level at baseline",
      value: "8.2",
      status: "DRAFT",
      createdDate: "2024-01-15T12:00:00Z",
      lastUpdatedDate: "2024-01-15T12:00:00Z",
    },
    {
      id: "7",
      label: "Concomitant Medications",
      name: "concomitant_medications",
      description: "Number of concomitant medications",
      value: "3",
      status: "PUBLISHED",
      createdDate: "2024-01-15T12:15:00Z",
      lastUpdatedDate: "2024-01-15T12:15:00Z",
    },
    {
      id: "8",
      label: "Visit Schedule",
      name: "visit_schedule",
      description: "Visit schedule pattern",
      value: "Week 0, 4, 8, 12, 16, 20, 24",
      status: "DRAFT",
      createdDate: "2024-01-15T12:30:00Z",
      lastUpdatedDate: "2024-01-15T12:30:00Z",
    },
  ]);

  const openAddModal = () => {
    setSelectedVariable(null);
    openModal();
  };

  const openEditModal = (variable: Variable) => {
    setSelectedVariable(variable);
    openModal();
  };

  const handleCloseModal = () => {
    closeModal();
    setSelectedVariable(null);
  };

  const handleSave = (data: any) => {
    if (data.id) {
      // Update existing variable
      setVariables((prev) =>
        prev.map((v) =>
          v.id === data.id
            ? { ...data, lastUpdatedDate: new Date().toISOString() }
            : v,
        ),
      );
    } else {
      // Add new variable
      const newVariable = {
        ...data,
        id: Date.now().toString(),
        status: "DRAFT" as const,
        createdDate: new Date().toISOString(),
        lastUpdatedDate: new Date().toISOString(),
      };
      setVariables((prev) => [...prev, newVariable]);
    }
  };

  const columns = useMemo(() => generateVariableColumns(openEditModal), []);

  const isPending = false; // Replace with actual loading state
  const isPlaceholderData = false; // Replace with actual placeholder state

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Variables
          </h3>

          <div className="flex items-center gap-4">
            {" "}
            <SearchField
              queryKey="variableSearch"
              placeholder="Search variables..."
            />
            <Button variant="primary" onClick={openAddModal}>
              <IoMdAdd className="h-4 w-4" /> New Variable
            </Button>
          </div>
        </div>
      </div>

      <div className="p-0">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={variables} />
            {/* <TableDataPagination metadata={}  /> */}
          </LoadingWrapper>
        )}
      </div>

      <VariableModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        selectedVariable={selectedVariable}
        onSave={handleSave}
      />
    </Card>
  );
};
