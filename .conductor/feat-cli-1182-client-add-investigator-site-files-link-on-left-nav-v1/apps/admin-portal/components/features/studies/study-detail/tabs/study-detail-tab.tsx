import { useParams } from "next/navigation";
import React, { useState } from "react";
import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

import { useStudy } from "../../hooks/use-studies-queries";
import { ModalEditStudy } from "../modal-edit-study";

export const StudyDetailTab = () => {
  const [isOpen, setIsOpen] = useState(false);

  const params = useParams();
  const id = params.id as string;
  const { data: study, isLoading: isLoadingStudy } = useStudy(id);

  return (
    <>
      {isLoadingStudy ? (
        <StudyContentSkeleton />
      ) : (
        <OverviewCard
          rightContent={
            <Button variant="primary" onClick={() => setIsOpen(true)}>
              <CiEdit />
              Edit Study
            </Button>
          }
          title="Overview"
        >
          <div className="grid grid-cols-2 gap-4 gap-x-2">
            <OverviewItem label="Name" value={study?.name ?? "N/A"} />
            <OverviewItem label="Status">
              <PillBadge variant={study?.isActive ? "success" : "default"}>
                {study?.isActive ? "Active" : "Inactive"}
              </PillBadge>
            </OverviewItem>
            <OverviewItem
              label="Start Date"
              value={study?.startDate ?? "N/A"}
            />
            <OverviewItem label="End Date" value={study?.endDate ?? "N/A"} />
          </div>
        </OverviewCard>
      )}
      <ModalEditStudy
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        study={study}
      />
    </>
  );
};

const StudyContentSkeleton = () => {
  return (
    <div className="flex flex-col">
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <Skeleton className="mb-3 h-6 w-24" />
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
};
