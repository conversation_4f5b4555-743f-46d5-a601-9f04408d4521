import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddProtocolStudyArmPayload,
  UpdateProtocolStudyArmsPayload,
} from "@/lib/apis/protocols/types";

import { studyArmKeys } from "./use-study-arms-queries";

export const useAddStudyArm = (protocolId: string) => {
  return useMutation({
    mutationFn: (data: Omit<AddProtocolStudyArmPayload, "protocolId">) =>
      api.protocols.addStudyArm({ ...data, protocolId }),
    onError: (error) => toast.error(error.message || "Failed to add study arm"),
    onSettled: (_, err) =>
      !err && toast.success("Study arm added successfully"),
    meta: {
      awaits: studyArmKeys.allLists(protocolId),
    },
  });
};

export const useUpdateStudyArm = (protocolId: string) => {
  return useMutation({
    mutationFn: (data: UpdateProtocolStudyArmsPayload) =>
      api.protocols.updateProtocolsStudyArm(data),
    onError: (error) =>
      toast.error(error.message || "Failed to update study arm"),
    onSettled: (_, err) =>
      !err && toast.success("Study arm updated successfully"),
    meta: {
      awaits: studyArmKeys.allLists(protocolId),
    },
  });
};
