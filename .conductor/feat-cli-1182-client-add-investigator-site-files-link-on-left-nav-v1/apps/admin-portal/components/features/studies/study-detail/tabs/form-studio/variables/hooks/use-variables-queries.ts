import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";
import { MetadataParams } from "@/lib/apis/types";

import { Variable } from "../columns";

export const variableKeys = {
  all: () => ["variables"] as const,
  allLists: () => [...variableKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...variableKeys.allLists(), params] as const,
};

export const useVariables = () => {
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const { search } = useSearch("variableSearch");

  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      search: search || undefined,
    },
  };

  return useQuery({
    queryKey: variableKeys.list(params),
    queryFn: () => {
      const mockVariables: Variable[] = [];

      return Promise.resolve({
        results: mockVariables,
        metadata: {
          currentPage: page || 1,
          totalPages: 1,
          totalCount: mockVariables.length,
          pageSize: take || 10,
        },
      });
    },
    placeholderData: (prev) => prev,
  });
};
