import { useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  CreateFieldPayload,
  UpdateFieldPayload,
} from "@/lib/apis/field-library/types";

import { fieldLibraryKeys } from "./use-field-library-queries";

export const useCreateField = () => {
  const { id: studyId } = useParams<{ id: string }>();

  return useMutation({
    mutationFn: (payload: Omit<CreateFieldPayload, "studyId">) =>
      api.fieldLibrary.create({ ...payload, studyId: studyId! }),
    onError: (err) => toast.error(err?.message || "Failed to create field"),
    onSettled: (_, err) => !err && toast.success("Field created successfully"),
    meta: {
      awaits: fieldLibraryKeys.allLists(),
    },
  });
};

export const useUpdateField = () => {
  const { id: studyId } = useParams<{ id: string }>();

  return useMutation({
    mutationFn: (payload: Omit<UpdateFieldPayload, "studyId">) =>
      api.fieldLibrary.update({ ...payload, studyId }),
    onError: (err) => toast.error(err?.message || "Failed to update field"),
    onSettled: (_, err) => !err && toast.success("Field updated successfully"),
    meta: {
      awaits: fieldLibraryKeys.allLists(),
    },
  });
};

export const useArchiveField = () =>
  useMutation({
    mutationFn: (id: string) => api.fieldLibrary.archive(id),
    onError: (err) => toast.error(err?.message || "Failed to archive field"),
    onSettled: (_, err) => !err && toast.success("Field archived successfully"),
    meta: {
      awaits: fieldLibraryKeys.allLists(),
    },
  });

export const usePublishField = () =>
  useMutation({
    mutationFn: (id: string) => api.fieldLibrary.publish(id),
    onError: (err) => toast.error(err?.message || "Failed to publish field"),
    onSettled: (_, err) =>
      !err && toast.success("Field published successfully"),
    meta: {
      awaits: fieldLibraryKeys.allLists(),
    },
  });

export const useDeleteField = () =>
  useMutation({
    mutationFn: (id: string) => api.fieldLibrary.delete(id),
    onError: (err) => toast.error(err?.message || "Failed to delete field"),
    onSettled: (_, err) => !err && toast.success("Field deleted successfully"),
    meta: {
      awaits: fieldLibraryKeys.allLists(),
    },
  });

export const useCreateFieldVersion = () =>
  useMutation({
    mutationFn: (id: string) => api.fieldLibrary.createVersion(id),
    onError: (err) =>
      toast.error(err?.message || "Failed to create field version"),
    onSettled: (_, err) =>
      !err && toast.success("Field version created successfully"),
    meta: {
      awaits: fieldLibraryKeys.allLists(),
    },
  });
