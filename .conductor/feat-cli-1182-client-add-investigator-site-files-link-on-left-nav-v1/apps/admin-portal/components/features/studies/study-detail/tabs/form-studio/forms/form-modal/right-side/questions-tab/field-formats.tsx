import { ToggleSwitch } from "flowbite-react";

type TextFormatProps = {
  maxLength: number;
};

export const TextFormat = ({ maxLength }: TextFormatProps) => {
  return (
    <div className="space-y-1">
      <span className="text-xs font-medium text-gray-900 dark:text-white">
        Maximum Character Length
      </span>
      <div className="flex rounded-lg border border-gray-300 bg-gray-50 px-4 py-2.5 text-base text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
        {maxLength ?? "--"}
      </div>
    </div>
  );
};

type DateTimeFormatProps = {
  dateFormat: string;
};

export const DateTimeFormat = ({ dateFormat }: DateTimeFormatProps) => {
  return (
    <div className="space-y-1">
      <span className="text-xs font-medium text-gray-900 dark:text-white">
        Date Format
      </span>
      <div className="flex rounded-lg border border-gray-300 bg-gray-50 px-4 py-2.5 text-base text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
        {dateFormat ?? "--"}
      </div>
    </div>
  );
};

type IntegerFormatProps = {
  minValue?: number;
  maxValue?: number;
  allowLeadingZeros: boolean;
};

export const IntegerFormat = ({
  minValue,
  maxValue,
  allowLeadingZeros,
}: IntegerFormatProps) => {
  return (
    <div className="space-y-5">
      <div className="grid grid-cols-2 gap-5">
        <div className="space-y-1">
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            Minimum Value
          </span>
          <div className="flex rounded-lg border border-gray-300 bg-gray-50 px-4 py-2.5 text-base text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            {minValue ?? "--"}
          </div>
        </div>
        <div className="space-y-1">
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            Maximum Value
          </span>
          <div className="flex rounded-lg border border-gray-300 bg-gray-50 px-4 py-2.5 text-base text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            {maxValue ?? "--"}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <span className="text-xs font-medium text-gray-900 dark:text-white">
          Allow Leading Zeros
        </span>
        <ToggleSwitch
          checked={allowLeadingZeros}
          onChange={() => {}}
          sizing="sm"
          className="cursor-default"
        />
      </div>
    </div>
  );
};

type DecimalFormatProps = {
  minValue?: number;
  maxValue?: number;
  decimalPlaces: number;
  allowTrailingZeros: boolean;
  allowLeadingZeros: boolean;
};

export const DecimalFormat = ({
  minValue,
  maxValue,
  decimalPlaces,
  allowTrailingZeros,
  allowLeadingZeros,
}: DecimalFormatProps) => {
  return (
    <div className="space-y-5">
      <div className="grid grid-cols-3 gap-5">
        <div className="space-y-1">
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            Minimum Value
          </span>
          <div className="flex rounded-lg border border-gray-300 bg-gray-50 px-4 py-2.5 text-base text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            {minValue ?? "--"}
          </div>
        </div>
        <div className="space-y-1">
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            Maximum Value
          </span>
          <div className="flex rounded-lg border border-gray-300 bg-gray-50 px-4 py-2.5 text-base text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            {maxValue ?? "--"}
          </div>
        </div>
        <div className="space-y-1">
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            Decimal Places
          </span>
          <div className="flex rounded-lg border border-gray-300 bg-gray-50 px-4 py-2.5 text-base text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            {decimalPlaces ?? "--"}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-5">
        <div className="flex items-center gap-2">
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            Allow Trailing Zeros
          </span>
          <ToggleSwitch
            checked={allowTrailingZeros}
            onChange={() => {}}
            sizing="sm"
            className="cursor-default"
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            Allow Leading Zeros
          </span>
          <ToggleSwitch
            checked={allowLeadingZeros}
            onChange={() => {}}
            sizing="sm"
            className="cursor-default"
          />
        </div>
      </div>
    </div>
  );
};
