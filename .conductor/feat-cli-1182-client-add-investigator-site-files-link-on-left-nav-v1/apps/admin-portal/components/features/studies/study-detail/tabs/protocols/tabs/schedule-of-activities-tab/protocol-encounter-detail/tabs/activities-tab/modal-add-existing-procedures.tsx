import { useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

import { useAddProcedureToActivity } from "@/components/features/studies/study-detail/tabs/activities/hooks/use-activities-mutations";
import { useInfiniteProcedures } from "@/components/features/studies/study-detail/tabs/procedures/hooks/use-procedures-queries";
import { Button, CloseButton } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazyMultipleSelect } from "@/components/ui/lazy-select/lazy-multiple-select";
import { Modal } from "@/components/ui/modal";

import { useActivityProcedures } from "../../hooks/use-activity-procedure";
import { USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY } from "../../hooks/use-encounter-activities";

type ModalAddProcedureProps = {
  activityId: string;
  isOpen: boolean;
  onClose: () => void;
};

export const addProcedureToActivitySchema = z.object({
  procedures: z
    .array(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        description: z.string().nullable().optional(),
        isActive: z.boolean().optional(),
      }),
    )
    .min(1, "Procedure is required"),
});

type AddProcedureFormValues = z.infer<typeof addProcedureToActivitySchema>;

export const ModalAddExistingProcedure = ({
  activityId,
  isOpen,
  onClose,
}: ModalAddProcedureProps) => {
  const queryClient = useQueryClient();
  const { data } = useActivityProcedures(activityId);

  const { mutateAsync: addProcedure, isPending: isAdding } =
    useAddProcedureToActivity();

  async function onSubmit(data: AddProcedureFormValues) {
    await addProcedure(
      {
        activityId,
        procedureIds: data.procedures.map((pro) => pro.id),
      },
      {
        onSuccess: () =>
          queryClient.invalidateQueries({
            queryKey: [USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY],
          }),
      },
    );
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Existing Procedure</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addProcedureToActivitySchema}
          onSubmit={onSubmit}
          defaultValues={{
            procedures: [],
          }}
        >
          <div className="space-y-3 sm:space-y-6">
            <div className="space-y-1">
              <Label htmlFor="procedures">Procedure</Label>
              <LazyMultipleSelect
                id="procedures"
                name="procedures"
                placeholder="Select procedures..."
                searchPlaceholder="Search procedures..."
                useInfiniteQuery={useInfiniteProcedures}
                getOptionLabel={(option) => option.name}
                getOptionValue={(option) => option.id}
                mapData={(options) => {
                  return options.filter(
                    (opt) => !data?.results.some((pro) => pro.id === opt.id),
                  );
                }}
              />
            </div>

            <div className="flex flex-col justify-end gap-4 border-none pt-0 sm:flex-row sm:gap-5">
              <CloseButton onClose={onClose} />
              <Button type="submit" variant="primary" isLoading={isAdding}>
                Add
              </Button>
            </div>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
