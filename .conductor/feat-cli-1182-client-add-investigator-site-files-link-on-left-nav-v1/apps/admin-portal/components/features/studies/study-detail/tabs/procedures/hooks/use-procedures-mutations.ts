import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import { useOptimisticMutation } from "@/hooks/use-optimistic-mutation";
import api from "@/lib/apis";
import type {
  CreateProcedurePayload,
  Procedure,
  ProcedureListResponse,
  ReorderProceduresPayload,
  UpdateProcedurePayload,
} from "@/lib/apis/procedures";
import { TAKE_ALL } from "@/lib/constants";

import { studyProcedureKeys } from "./use-procedures-queries";

export const useImportProcedures = (studyId: string) => {
  return useMutation({
    mutationFn: async (payload: { formData: FormData; id: string }) => {
      return api.procedures.import(payload);
    },
    onSettled: (_, err) => {
      if (!err) {
        toast.success("Import procedures successfully");
      }
    },
    onError: (error) => {
      toast.error(error?.message || "Fail to import procedures");
    },
    meta: {
      awaits: studyProcedureKeys.allList(studyId),
    },
  });
};

export const useAddProcedure = (studyId: string) => {
  return useMutation({
    mutationFn: async (data: CreateProcedurePayload) => {
      return api.procedures.create(data);
    },
    onSettled: (_, err) => {
      if (!err) {
        toast.success("Procedure added successfully");
      }
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: {
      awaits: studyProcedureKeys.allList(studyId),
    },
  });
};

export const useEditProcedure = (studyId: string) => {
  return useMutation({
    mutationFn: async (data: UpdateProcedurePayload & { id: string }) => {
      const { id, ...payload } = data;
      return api.procedures.update(id, payload);
    },
    onSettled: (_, err) => {
      if (!err) {
        toast.success("Procedure updated successfully");
      }
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: {
      awaits: studyProcedureKeys.allList(studyId),
    },
  });
};

export const useReorderProcedures = (studyId: string) => {
  return useOptimisticMutation({
    queryKey: studyProcedureKeys.list(studyId, {
      take: TAKE_ALL,
    }),
    invalidates: studyProcedureKeys.allList(studyId),
    mutationFn: (
      payload: ReorderProceduresPayload & {
        newOrderedProcedures: Procedure[];
      },
    ) => api.procedures.reorderProcedures(payload),
    onMutate: () => {
      toast.success("Procedures reordered successfully");
    },
    updater: (oldData: ProcedureListResponse | undefined, payload) => {
      if (!oldData) return undefined;
      return {
        ...oldData,
        results: payload.newOrderedProcedures,
      };
    },
    onError: (error) => {
      toast.error(error.message ?? "Failed to reorder procedures");
    },
  });
};
