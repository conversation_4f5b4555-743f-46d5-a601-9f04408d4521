import { LaptopMinimal, Minus, Type } from "lucide-react";
import React, { useMemo } from "react";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { generateQuestionColumns } from "./table-columns";
import { Question, useQuestions } from "./use-questions-queries";

type Props = {
  selectedFolderId: string | null;
  draggingQuestion: Question | null;
  isMovingQuestion: boolean;
};

export const QuestionsTable = ({
  selectedFolderId,
  draggingQuestion,
  isMovingQuestion,
}: Props) => {
  const { data, isPending, isPlaceholderData } = useQuestions(selectedFolderId);

  const columns = useMemo(() => generateQuestionColumns(), []);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2.5">
          <button className="rounded-lg border border-gray-300 p-2.5 hover:bg-gray-200">
            <Minus />
          </button>
          <button className="rounded-lg border border-gray-300 p-2.5 hover:bg-gray-200">
            <LaptopMinimal className="rotate-180" />
          </button>
          <button className="rounded-lg border border-gray-300 p-2.5 hover:bg-gray-200">
            <Type />
          </button>
        </div>
        <SearchField placeholder="Search" className="max-w-[200px]" />
      </div>
      <div className="flex-1 overflow-hidden rounded-lg border border-gray-300 dark:border-gray-500">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isMovingQuestion || isPlaceholderData}>
            <Table
              columns={columns}
              data={data?.results ?? []}
              draggingRowId={draggingQuestion?.id || ""}
              enableSorting={false}
            />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </div>
    </div>
  );
};
