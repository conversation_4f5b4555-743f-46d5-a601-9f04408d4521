"use client";

import type { DragEndEvent } from "@dnd-kit/core";
import { useQueryClient } from "@tanstack/react-query";
import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useRef, useState } from "react";
import { CiEdit } from "react-icons/ci";
import { FaRegCalendarPlus } from "react-icons/fa6";
import { MdPublish } from "react-icons/md";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import type { ModalWarningRef } from "@/components/ui/modal-warning";
import { ModalWarning } from "@/components/ui/modal-warning";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableLoading } from "@/components/ui/table";
import type { VisitTemplate } from "@/lib/apis/visit-schedules";

import { useStudy } from "../../hooks/use-studies-queries";
import { usePublishVisitSchedule } from "../hooks/use-publish-visit-schedule";
import { useUpdateVisitSchedule } from "../hooks/use-update-visit-schedule";
import {
  USE_VISIT_SCHEDULE_QUERY_KEY,
  useVisitSchedule,
} from "../hooks/use-visit-schedule";
import { generateVisitTemplateColumns } from "./columns";
import {
  type AddVisitTemplatePayload,
  ModalAddVisitTemplate,
} from "./modal-add-visit-template";
import { ModalEditVisitTemplate } from "./modal-edit-visit-template";

type VisitTemplateExtended = VisitTemplate & {
  isAdded?: boolean;
};

export const VisitScheduleEditorContent = () => {
  const { id: studyId, visitId } = useParams();
  const queryClient = useQueryClient();
  const modalWarningRef = useRef<ModalWarningRef>(null);
  const [selectedVisitTemplate, setSelectedVisitTemplate] =
    useState<VisitTemplate | null>(null);
  const [isModalEditVisitTemplateOpen, setIsModalEditVisitTemplateOpen] =
    useState(false);

  const { data: study, isLoading: isLoadingStudy } = useStudy(
    studyId as string,
  );
  const { data: visitSchedule, isLoading } = useVisitSchedule(
    visitId as string,
  );
  const { mutate: publishVisitSchedule, isPending: isPublishing } =
    usePublishVisitSchedule();
  const { mutateAsync: updateVisitSchedule, isPending: isUpdating } =
    useUpdateVisitSchedule();

  const breadcrumbItems = [
    { label: "Studies", href: "/studies" },
    {
      label: study?.name ?? "Study Detail",
      href: `/studies/${studyId}`,
      loading: isLoadingStudy,
    },
    { label: "Visit Schedule Editor" },
  ];

  const [name, setName] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(name);
  const inputRef = useRef<HTMLInputElement>(null);
  const memoizedColumns = useMemo(
    () =>
      generateVisitTemplateColumns((visitTemplate) => {
        setSelectedVisitTemplate(visitTemplate);
        setIsModalEditVisitTemplateOpen(true);
      }),
    [],
  );
  const [data, setData] = useState<VisitTemplateExtended[]>([]);

  const [maxVisitOrder, setMaxVisitOrder] = useState(0);
  const [isModalAddVisitTemplateOpen, setIsModalAddVisitTemplateOpen] =
    useState(false);
  const [isAddingVisitTemplate, setIsAddingVisitTemplate] = useState(false);

  useEffect(() => {
    let listVisitTemplates = visitSchedule?.visitTemplates ?? [];
    listVisitTemplates = listVisitTemplates.map((v) => ({
      ...v,
      visitTemplateId: v.id,
    }));
    setData(listVisitTemplates);
    setName(visitSchedule?.name ?? "");
  }, [visitSchedule]);

  useEffect(() => {
    if (data.length === 0) return;
    setMaxVisitOrder(Math.max(...data.map((v) => v.visitOrder)));
  }, [data]);

  useEffect(() => {
    if (isEditing) {
      inputRef.current?.focus();
    }
  }, [isEditing]);

  const handleDragEnd = (event: DragEndEvent) => {
    if (!visitSchedule) return;
    const { active, over } = event;

    if (!active || !over || active.id === over.id) {
      return;
    }

    const oldIndex = data.findIndex((item) => item.id === active.id);
    const newIndex = data.findIndex((item) => item.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    const newData = [...data];
    const [movedItem] = newData.splice(oldIndex, 1);
    newData.splice(newIndex, 0, movedItem);

    newData.forEach((item, index) => {
      item.visitOrder = index + 1;
    });

    updateVisitSchedule({
      id: visitSchedule.id,
      name: visitSchedule.name,
      visitTemplates: newData,
    });
    setData(newData);
  };

  const handleAddVisitTemplate = async (payload: AddVisitTemplatePayload) => {
    if (!visitSchedule) return;
    setIsAddingVisitTemplate(true);
    const newVisitTemplate = {
      name: payload.name,
      description: payload.description,
      visitOrder: maxVisitOrder + 1,
    };

    try {
      await updateVisitSchedule({
        id: visitSchedule.id,
        name: visitSchedule.name,
        visitTemplates: [...data, newVisitTemplate],
      });
      await queryClient.invalidateQueries({
        queryKey: [USE_VISIT_SCHEDULE_QUERY_KEY, visitSchedule.id],
      });
    } catch (err) {
      console.error(err);
    } finally {
      setIsAddingVisitTemplate(false);
    }
    setIsModalAddVisitTemplateOpen(false);
  };

  if (isLoading) {
    return (
      <div className="p-6 px-4">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex items-center justify-between">
          <PageHeader showBackButton href="/studies">
            Visit Schedule Editor
          </PageHeader>
        </div>

        <Card className="[&>div]:p-0">
          <div className="mb-4 p-4 pb-0">
            <Skeleton className="h-8 w-[300px]" />
          </div>
          <TableLoading columns={memoizedColumns} />
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="p-6">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex items-center justify-between">
          <PageHeader showBackButton href="/studies">
            Visit Schedule Editor
          </PageHeader>
        </div>

        <Card className="[&>div]:p-0">
          <div className="mb-4 flex items-center justify-between gap-3 p-4 pb-0 text-lg font-semibold dark:text-gray-400">
            {isEditing ? (
              <div className="flex items-center gap-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  className="ring-primary-400 min-w-[300px] rounded border-none ring-4"
                />
                <Button
                  variant="primary"
                  disabled={isUpdating}
                  isLoading={isUpdating}
                  onClick={async () => {
                    if (!visitSchedule) return;
                    setName(editedName);
                    await updateVisitSchedule({
                      id: visitSchedule.id,
                      name: editedName,
                    });
                    setIsEditing(false);
                  }}
                >
                  Save
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                {name}
                <CiEdit
                  className="size-7 cursor-pointer"
                  onClick={() => {
                    setEditedName(name);
                    setIsEditing(true);
                  }}
                />
              </div>
            )}
            <Button
              variant="primary"
              disabled={
                isPublishing || !visitSchedule || visitSchedule.publishedBy
              }
              onClick={() => {
                if (!visitSchedule) return;
                modalWarningRef.current?.toggleModal();
              }}
            >
              <MdPublish />
              Publish
            </Button>
          </div>
          <Table
            columns={memoizedColumns}
            data={data}
            draggable={{ enable: !visitSchedule?.publishedBy, handleDragEnd }}
          />
          <Button
            variant="primary"
            className="mx-4 my-4 w-fit"
            onClick={() => setIsModalAddVisitTemplateOpen(true)}
            disabled={visitSchedule?.publishedBy}
          >
            <FaRegCalendarPlus />
            Add Visit
          </Button>
        </Card>
      </div>
      <ModalAddVisitTemplate
        isOpen={isModalAddVisitTemplateOpen}
        onClose={() => setIsModalAddVisitTemplateOpen(false)}
        onSubmit={handleAddVisitTemplate}
        isAdding={isAddingVisitTemplate}
      />
      <ModalWarning
        ref={modalWarningRef}
        title="Warning"
        description="Once published, you will not be able to edit this Visit Schedule anymore. Continue?"
        onAccept={async () => {
          if (!visitSchedule) return;
          await publishVisitSchedule({ id: visitSchedule.id, published: true });
          modalWarningRef.current?.toggleModal();
        }}
      />
      {selectedVisitTemplate && (
        <ModalEditVisitTemplate
          isOpen={isModalEditVisitTemplateOpen}
          onClose={() => setIsModalEditVisitTemplateOpen(false)}
          defaultValues={selectedVisitTemplate}
        />
      )}
    </>
  );
};
