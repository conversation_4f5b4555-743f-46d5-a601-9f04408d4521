import type { ColumnDef } from "@tanstack/react-table";
import { CiEdit } from "react-icons/ci";
import { FaBars } from "react-icons/fa";

import type { VisitTemplate } from "@/lib/apis/visit-schedules";

export const generateVisitTemplateColumns = (
  onEdit: (visitTemplate: VisitTemplate) => void,
) => {
  const columns: ColumnDef<VisitTemplate>[] = [
    {
      id: "drag",
      header: "",
      cell: () => (
        <div className="w-fit cursor-grab">
          <FaBars className="h-4 w-4" />
        </div>
      ),
      meta: {
        width: "60px",
      },
    },
    {
      header: "Name",
      accessorKey: "name",
    },
    {
      header: "Description",
      accessorKey: "description",
    },
    {
      header: "Action",
      accessorKey: "id",
      cell: ({ row }) => {
        return (
          <button
            className="flex gap-4"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(row.original);
            }}
          >
            <div className="leading-4.5 text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium">
              <span className="whitespace-nowrap">Edit</span>
              <CiEdit />
            </div>
          </button>
        );
      },
      enableSorting: false,
    },
  ];

  return columns;
};
