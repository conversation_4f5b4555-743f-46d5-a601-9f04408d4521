import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import type { User } from "@/lib/apis/users/types";

import { useUpdateUser } from "../hooks/use-users-mutations";
import { useInfiniteUserProfiles } from "./tabs/profiles/hooks/use-user-profiles-queries";

const editUserSchema = z.object({
  firstName: z
    .string({ required_error: "First name is required" })
    .min(1, "First name is required"),
  lastName: z
    .string({ required_error: "Last name is required" })
    .min(1, "Last name is required"),
  email: z
    .string({ required_error: "Email is required" })
    .email("Invalid email format"),
  phone: z
    .string({ required_error: "Phone is required" })
    .min(1, "Phone is required"),
  profileId: z
    .string({ required_error: "Profile is required" })
    .min(1, "Profile is required"),
  profileColor: z.string().optional(),
});

type ModalEditUserProps = {
  isOpen: boolean;
  onClose: () => void;
  user?: User;
};

export const ModalEditUser = function ({
  isOpen,
  onClose,
  user,
}: ModalEditUserProps) {
  const { mutateAsync: updateUser, isPending } = useUpdateUser(user?.id || "");
  async function onSubmit(data: z.infer<typeof editUserSchema>) {
    if (!user?.id) return;
    await updateUser(data);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Edit User</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={editUserSchema}
          onSubmit={onSubmit}
          defaultValues={{
            profileId: user?.currentProfileId,
            profileColor: user?.userSettings?.profileIcon?.color,
            firstName: user?.firstName || "",
            lastName: user?.lastName || "",
            email: user?.email || "",
            phone: user?.phone || "",
          }}
          formProps={{ shouldFocusError: false }}
        >
          <UserForm
            onClose={onClose}
            isSubmitting={isPending}
            userId={user?.id}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type UserFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
  userId?: string;
};
const PROFILE_COLORS = [
  {
    name: "teal",
    color: "#0694A2",
  },
  {
    name: "green",
    color: "#31C48D",
  },
  {
    name: "orange",
    color: "#FF8A4C",
  },
  {
    name: "blue",
    color: "#3F83F8",
  },
  {
    name: "purple",
    color: "#9061F9",
  },
  {
    name: "red",
    color: "#F98080",
  },
  {
    name: "gray",
    color: "#667085",
  },
] as const;

const UserForm = ({ onClose, isSubmitting, userId }: UserFormProps) => {
  return (
    <>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="firstName">First Name</Label>
          <InputField
            id="firstName"
            name="firstName"
            placeholder="Enter first name"
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="lastName">Last Name</Label>
          <InputField
            id="lastName"
            name="lastName"
            placeholder="Enter last name"
          />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="email">Email</Label>
          <InputField id="email" name="email" placeholder="Enter email" />
        </div>

        <div className="col-span-1 flex flex-col gap-2">
          <Label htmlFor="phone">Phone</Label>
          <InputField id="phone" name="phone" placeholder="Enter phone" />
        </div>

        <div className="flex flex-col gap-2 sm:col-span-2">
          <Label htmlFor="profileId">Profile</Label>
          <LazySelect
            name="profileId"
            id="profileId"
            placeholder="Select profile"
            searchPlaceholder="Search profiles..."
            useInfiniteQuery={useInfiniteUserProfiles}
            getOptionLabel={(profile) => profile.name}
            getOptionValue={(profile) => profile.id}
            params={[userId]}
          />
        </div>
        <div className="flex flex-col gap-2 sm:col-span-2">
          <Label htmlFor="profileColor">Profile Color</Label>
          <Select
            name="profileColor"
            id="profileColor"
            placeholder="Select profile"
            options={PROFILE_COLORS.map((color) => ({
              label: (
                <div className="flex items-center gap-2 capitalize">
                  <div
                    className="size-3 rounded-full"
                    style={{ backgroundColor: color.color }}
                  />
                  {color.name}
                </div>
              ),
              value: color.color,
            }))}
          />
        </div>
      </div>

      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
