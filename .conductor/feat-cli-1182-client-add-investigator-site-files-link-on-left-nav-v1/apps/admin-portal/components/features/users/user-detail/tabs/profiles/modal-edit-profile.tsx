import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteRoles } from "@/hooks/queries/use-infinite-roles";
import { Profile } from "@/lib/apis/groups/types";
import { capitalize } from "@/utils/string";

import { useUpdateRoleProfile } from "./hooks/use-user-profiles-mutations";

const addProfileSchema = z.object({
  roleId: z
    .string({ required_error: "Role is required" })
    .min(1, "Role is required"),
});

type Props = {
  selectedProfile: Profile;
  onClose: () => void;
};

export const ModalEditProfile = ({ selectedProfile, onClose }: Props) => {
  const { mutateAsync, isPending } = useUpdateRoleProfile(
    selectedProfile.userId,
  );

  const handleSubmit = async (values: z.infer<typeof addProfileSchema>) => {
    await mutateAsync({
      roleId: values.roleId,
      profileId: selectedProfile.rolesProfiles[0].profileId,
    });
    onClose();
  };

  return (
    <Modal show={!!selectedProfile} onClose={onClose}>
      <Modal.Header>Edit Profile</Modal.Header>
      <Modal.Body>
        <Form
          schema={addProfileSchema}
          onSubmit={handleSubmit}
          defaultValues={{
            roleId: selectedProfile.rolesProfiles[0].roleId,
          }}
          className="space-y-4"
        >
          <div className="flex flex-col gap-2">
            <Label htmlFor="roleId">Role</Label>
            <LazySelect
              name="roleId"
              id="roleId"
              searchPlaceholder="Search role..."
              useInfiniteQuery={useInfiniteRoles}
              getOptionLabel={(role) =>
                `${role.name} (${capitalize(role.type)})`
              }
              getOptionValue={(role) => role.id}
              params={[
                selectedProfile.currentGroup.type === "clincove"
                  ? "admin"
                  : selectedProfile.currentGroup.type,
              ]}
              placeholder="Select role"
            />
          </div>

          <div className="flex flex-col justify-end gap-4 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              disabled={isPending}
              variant="primary"
              enabledForDirty
              disabledForInvalid
              isLoading={isPending}
            >
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
