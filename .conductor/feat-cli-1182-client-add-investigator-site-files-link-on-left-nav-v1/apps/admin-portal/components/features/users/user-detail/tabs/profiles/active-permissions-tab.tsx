"use client";

import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { Dispatch, SetStateAction, useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { Profile } from "@/lib/apis/groups/types";

import { ModalAddStudy } from "./add-study-modal";
import { generateActivePermissionColumns } from "./columns";
import { useDeleteUserCustomPermission } from "./hooks/use-user-profiles-mutations";
import { useUserCustomPermission } from "./hooks/use-user-profiles-queries";

export const ActivePermissions = ({
  groupId,
  selectedProfile,
  setSelectedProfile,
}: {
  groupId: string;
  selectedProfile: Profile;
  setSelectedProfile: Dispatch<SetStateAction<Profile | null>>;
}) => {
  const userId = useParams()?.id as string;

  const [isOpen, setIsOpen] = useState(false);
  const { data, isPending } = useUserCustomPermission(
    userId,
    selectedProfile.id,
  );
  const { mutateAsync, isPending: isRemoving } =
    useDeleteUserCustomPermission(userId);

  const columns = useMemo(
    () =>
      generateActivePermissionColumns((studyId: string) => {
        mutateAsync(
          {
            studyId,
            profileId: selectedProfile.id,
          },
          {
            onSuccess: (response) => {
              setSelectedProfile((prev) =>
                prev
                  ? {
                      ...prev,
                      hasCustomPermissions: response.hasCustomStudyPermissions,
                    }
                  : null,
              );
            },
          },
        );
      }),
    [mutateAsync, selectedProfile.id, setSelectedProfile, userId],
  );

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
        <div className="dark:text-gray-400">Studies</div>
        <Button variant="primary" onClick={() => setIsOpen(true)}>
          <IoMdAdd />
          Add Study
        </Button>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isRemoving}>
          <TableData columns={columns} data={data?.accessibleStudies ?? []} />
        </LoadingWrapper>
      )}

      <ModalAddStudy
        setSelectedProfile={setSelectedProfile}
        groupId={groupId}
        isOpen={isOpen}
        selectedProfile={selectedProfile}
        onClose={() => setIsOpen(false)}
      />
    </Card>
  );
};
