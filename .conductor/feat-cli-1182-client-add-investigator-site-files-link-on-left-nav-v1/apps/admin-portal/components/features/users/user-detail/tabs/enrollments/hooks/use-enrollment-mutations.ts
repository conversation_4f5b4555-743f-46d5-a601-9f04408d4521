import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddEnrollmentPayload,
  UpdateEnrollmentPayload,
} from "@/lib/apis/training-modules";

import { enrollmentByUserKeys } from "./use-enrollment-queries";

export const useAddEnrollment = (userId: string) =>
  useMutation({
    mutationFn: (payload: Omit<AddEnrollmentPayload, "userId">) =>
      api.trainingModules.addEnrollment({ ...payload, userId }),
    onError: (err) => toast.error(err?.message || "Fail to add enrollment"),
    onSettled: (_, err) => !err && toast.success("Add enrollment successfully"),
    meta: {
      awaits: enrollmentByUserKeys.allLists(userId),
    },
  });

export const useUpdateEnrollment = (userId: string) =>
  useMutation({
    mutationFn: (payload: UpdateEnrollmentPayload) =>
      api.trainingModules.updateEnrollment(payload),
    onError: (err) => toast.error(err?.message || "Fail to update enrollment"),
    onSettled: (_, err) =>
      !err && toast.success("Update enrollment successfully"),
    meta: {
      awaits: enrollmentByUserKeys.allLists(userId),
    },
  });

export const useDeleteEnrollment = (userId: string) =>
  useMutation({
    mutationFn: (id: string) => api.trainingModules.deleteEnrollment(id),
    onError: (err) => toast.error(err?.message || "Fail to delete enrollment"),
    onSettled: (_, err) =>
      !err && toast.success("Delete enrollment successfully"),
    meta: {
      awaits: enrollmentByUserKeys.allLists(userId),
    },
  });
