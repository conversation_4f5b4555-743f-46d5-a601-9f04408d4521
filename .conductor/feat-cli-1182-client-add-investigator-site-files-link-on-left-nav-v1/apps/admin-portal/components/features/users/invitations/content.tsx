"use client";

import type { SortingState } from "@tanstack/react-table";
import { Card } from "flowbite-react";
import { type FC, useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { UserInvitation } from "@/lib/apis/users/types";

import { useRevokeUser } from "../hooks/use-users-mutations";
import { useUserInvitations } from "../hooks/use-users-queries";
import { generateInvitationsColumns } from "./columns";
import { ModalRevokeInvitation } from "./modal-revoke-invitation";

const BREADCRUMB_ITEMS = [{ label: "Invitations" }];

const UserInvitationsContent: FC = function () {
  const [selectedInvitation, setSelectedInvitation] =
    useState<UserInvitation | null>(null);
  const { data, isPending, isPlaceholderData } = useUserInvitations();

  const { mutateAsync, isPending: isRevoking } = useRevokeUser();

  const handleRevokeClick = (invitation: UserInvitation) => {
    setSelectedInvitation(invitation);
  };

  const columns = useMemo(
    () => generateInvitationsColumns(handleRevokeClick, isRevoking),
    [isRevoking],
  );

  const exportCSVData = useMemo(() => {
    if (!data || !data.data) return [];

    const headers = [
      { label: "Name", key: "name" },
      { label: "Email", key: "emailAddress" },
      { label: "Status", key: "status" },
      { label: "Created Date", key: "createdAt" },
      { label: "Last Updated", key: "updatedAt" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.data.map((invitation) => [
        invitation.publicMetadata
          ? `${invitation.publicMetadata.firstName} ${invitation.publicMetadata.lastName}`.trim()
          : "-",
        invitation.emailAddress,
        invitation.status,
        invitation.createdAt,
        invitation.updatedAt,
      ]),
    ];
  }, [data]);

  return (
    <div className="space-y-4">
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>User Invitations</PageHeader>
      <Card className="[&>div]:p-0">
        <HeaderActions data={exportCSVData} filename="user-invitations.csv" />
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table data={data?.data ?? []} columns={columns} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </Card>

      <ModalRevokeInvitation
        isOpen={!!selectedInvitation}
        onClose={() => setSelectedInvitation(null)}
        invitation={selectedInvitation}
        onConfirm={mutateAsync}
        isRevoking={isRevoking}
      />
    </div>
  );
};

export default UserInvitationsContent;
