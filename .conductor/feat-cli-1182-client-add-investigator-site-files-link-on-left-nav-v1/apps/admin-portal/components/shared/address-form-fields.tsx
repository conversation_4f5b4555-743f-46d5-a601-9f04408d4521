import type { ComponentProps } from "react";
import { z } from "zod";

import { SelectCountries, SelectProvinces } from "../ui/form";
import { InputField } from "../ui/form/input";
import { Label } from "../ui/form/label";

export const addressSchema = z.object({
  addressLine: z
    .string({ required_error: "Address line is required" })
    .min(1, "Address line is required"),
  city: z
    .string({ required_error: "City is required" })
    .min(1, "City is required"),
  countryId: z
    .string({ required_error: "Country is required" })
    .min(1, "Country is required"),
  stateProvinceId: z
    .string({ required_error: "State/Province is required" })
    .min(1, "State/Province is required"),
  zipPostalCode: z
    .string({ required_error: "Zip/Postal code is required" })
    .min(1, "Zip/Postal code is required"),
});

type Props = {
  isShowRoleField?: boolean;
};

export const AddressFormFields = ({ isShowRoleField = false }: Props) => {
  return (
    <>
      <AddressFormField
        label="Address Line"
        name="address.addressLine"
        placeholder="Enter address line"
      />
      <div className="space-y-1">
        <Label required htmlFor="address.stateProvinceId">
          State/Province
        </Label>
        <SelectProvinces name="address.stateProvinceId" />
      </div>
      <AddressFormField
        label="City"
        name="address.city"
        placeholder="Enter city"
      />
      <div className="space-y-1">
        <Label required htmlFor="address.countryId">
          Country
        </Label>
        <SelectCountries name="address.countryId" />
      </div>
      <AddressFormField
        label="ZIP/Postal Code"
        name="address.zipPostalCode"
        placeholder="Enter zip/postal code"
      />
      {isShowRoleField && (
        <div className="space-y-1">
          <Label htmlFor="role">Role</Label>
          <InputField id="role" name="role" placeholder="Enter role" />
        </div>
      )}
    </>
  );
};

const AddressFormField = ({
  label,
  ...props
}: ComponentProps<typeof InputField> & { label: string }) => {
  return (
    <div className="space-y-1">
      <Label required htmlFor={props.name}>
        {label}
      </Label>
      <InputField {...props} />
    </div>
  );
};
