import parse from "emailjs-mime-parser";
import { useEffect, useState } from "react";

import { DocumentError } from "../error-document";
import { LoadingDocument } from "../loading-document";
import { EmailAttachments } from "./email-attachments";
import { EmailBody } from "./email-body";
import { EmailHeader } from "./email-header";
import { convertMimeNodeToEmailContent } from "./utils";

export type EmailAddress = {
  name?: string;
  email: string;
};

export type EmailAttachment = {
  filename: string;
  contentType: string;
  size: number;
  content?: Uint8Array;
  contentId?: string;
  contentDisposition?: string;
};

export type EmailContent = {
  from?: EmailAddress[];
  to?: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  subject?: string;
  date?: string;
  html?: string;
  text?: string;
  attachments?: EmailAttachment[];
  headers?: Record<string, string>;
};

export const EmlViewer = ({ url }: { url: string }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [emailContent, setEmailContent] = useState<EmailContent | null>(null);

  useEffect(() => {
    const fetchEmlContent = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to fetch EML file`);
        }

        const emlText = await response.text();

        try {
          // Parse the EML content using emailjs-mime-parser
          if (!emlText || typeof emlText !== "string") {
            throw new Error("Invalid email content received");
          }

          const parsedMimeNode = parse(emlText);
          if (!parsedMimeNode) {
            throw new Error("Failed to parse email structure");
          }

          const parsedEmail = convertMimeNodeToEmailContent(parsedMimeNode);
          setEmailContent(parsedEmail);
        } catch (err) {
          console.error("Error parsing EML file:", err);
          setError((err as Error).message || "Failed to parse email content");
        } finally {
          setLoading(false);
        }
      } catch (err) {
        console.error("Error loading EML file:", err);
        setError((err as Error).message || "Failed to load email content");
        setLoading(false);
      }
    };
    fetchEmlContent();
  }, [url]);

  if (loading) {
    return <LoadingDocument />;
  }

  if (error) {
    return <DocumentError />;
  }

  return (
    <div className="eml-viewer h-full w-full overflow-auto">
      {emailContent && (
        <div className="email-container mx-auto max-w-4xl rounded-lg bg-white p-6 shadow dark:bg-gray-800">
          <EmailHeader emailContent={emailContent} />
          <EmailBody emailContent={emailContent} />
          {emailContent.attachments && emailContent.attachments.length > 0 && (
            <EmailAttachments attachments={emailContent.attachments} />
          )}
        </div>
      )}
    </div>
  );
};
