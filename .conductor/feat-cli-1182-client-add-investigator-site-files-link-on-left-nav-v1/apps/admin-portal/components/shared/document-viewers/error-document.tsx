import React from "react";
import { BiError } from "react-icons/bi";
type Props = {
  message?: string;
};

export const DocumentError = ({ message }: Props) => {
  return (
    <div className="flex h-full flex-col items-center justify-center gap-2 text-red-600">
      <BiError className="size-10" />
      <div className="text-lg font-semibold">
        {message || "Fail to load document"}
      </div>
    </div>
  );
};
