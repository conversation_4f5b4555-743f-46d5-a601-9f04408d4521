import { useQuery } from "@tanstack/react-query";
import { Spin<PERSON>, theme } from "flowbite-react";
import { useState } from "react";
import { BiError } from "react-icons/bi";
import { IoMdClose } from "react-icons/io";
import { LuMaximize2, LuMinimize2 } from "react-icons/lu";

import { DOCUMENT_ICONS, DocumentType } from "@/components/icons/doc-icons";
import { DocumentViewer } from "@/components/shared/document-viewers/document-viewer";
import { DocumentTypeBadge } from "@/components/ui/badges/document-type-badge";
import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { cn } from "@/lib/utils";

type DocumentPreviewData = {
  id: string;
  title: string;
  extension: string;
};

type Props = {
  isOpen: boolean;
  onClose: () => void;
  document: DocumentPreviewData;
  fetchPreviewUrl: (documentId: string) => Promise<{ url: string }>;
};

export const DocumentPreviewModal = ({
  isOpen,
  onClose,
  document,
  fetchPreviewUrl,
}: Props) => {
  const [isModalFullscreen, setIsModalFullscreen] = useState(false);

  const {
    data: previewData,
    isPending,
    error,
  } = useQuery({
    queryKey: [document],
    queryFn: () => fetchPreviewUrl(document.id),
    enabled: isOpen && !!document?.id,
  });

  const handleRenderPreviewDocument = () => {
    if (isPending) {
      return (
        <div className="grid h-full place-content-center dark:bg-inherit">
          <Spinner size="xl" color="blue" className="fill-primary-500" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex h-full items-center justify-center gap-2 dark:bg-inherit">
          <BiError className="size-8 text-red-500" />
          <span className="text-red-500">
            {error?.message || "Failed to load document"}
          </span>
        </div>
      );
    }

    if (!previewData?.url) {
      return (
        <div className="flex h-full items-center justify-center gap-2 dark:bg-inherit">
          <BiError className="size-8 text-gray-500" />
          <span className="text-gray-500">No preview available</span>
        </div>
      );
    }

    return (
      <DocumentViewer
        type={document.extension as DocumentType}
        url={previewData.url}
      />
    );
  };

  return (
    <Modal
      theme={{
        content: {
          inner: cn(
            theme.modal.content.inner,
            isModalFullscreen && "fixed inset-0 max-h-full w-full",
          ),
        },
        body: {
          base: cn(theme.modal.body.base, isModalFullscreen && "!max-h-full"),
        },
      }}
      size="7xl"
      show={isOpen}
      onClose={onClose}
    >
      <Modal.Header className="border-b-0 !py-0 [&>button]:hidden [&>h3]:w-full">
        <div className="flex flex-col justify-between gap-y-4 pt-3 lg:flex-row">
          <div className="order-2 flex items-center gap-3 sm:order-1 sm:gap-5">
            {document.extension && (
              <DocumentTypeBadge
                className="text-base"
                type={document.extension}
              />
            )}
            <p className="flex gap-2 sm:gap-3">
              {document.extension &&
                DOCUMENT_ICONS[document.extension as DocumentType]}
              <span className="text-sm font-bold">
                {document?.title || "Document Preview"}
              </span>
            </p>
          </div>

          <div className="order-1 flex items-center justify-end gap-3 sm:order-2">
            <Button
              onClick={() => setIsModalFullscreen(!isModalFullscreen)}
              variant="primary"
              className="w-fit rounded-full !px-0"
            >
              {isModalFullscreen ? (
                <LuMinimize2 className="h-5 w-5" />
              ) : (
                <LuMaximize2 className="h-5 w-5" />
              )}
            </Button>
            <button onClick={onClose}>
              <IoMdClose className="size-6 text-gray-500" />
            </button>
          </div>
        </div>
      </Modal.Header>
      <Modal.Body className="grid max-h-[70vh] min-h-[70vh] grid-rows-1 gap-x-2 overflow-hidden border-none p-4">
        <div className="overflow-auto">{handleRenderPreviewDocument()}</div>
      </Modal.Body>
    </Modal>
  );
};
