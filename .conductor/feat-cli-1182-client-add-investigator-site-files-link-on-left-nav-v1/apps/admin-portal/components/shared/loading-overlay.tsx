"use client";

import { Spinner } from "flowbite-react";

import { cn } from "@/lib/utils";

type LoadingOverlayProps = {
  isLoading: boolean;
  className?: string;
};

export const LoadingOverlay = ({
  isLoading,
  className,
}: LoadingOverlayProps) => {
  if (!isLoading) return null;

  return (
    <div
      className={cn(
        "backdrop-blur-sm, className)}> fixed inset-0 z-[9999] flex items-center justify-center bg-gray-900/50",
        className,
      )}
    >
      <Spinner size="xl" />
    </div>
  );
};
