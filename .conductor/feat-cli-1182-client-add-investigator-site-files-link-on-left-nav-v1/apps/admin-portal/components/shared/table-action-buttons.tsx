import Link from "next/link";
import React from "react";
import { <PERSON>d<PERSON>ele<PERSON>, MdOutlineEdit, MdOutlineRemoveRedEye } from "react-icons/md";

import { cn } from "@/lib/utils";

type GeneralProps =
  | {
      type: "button";
      onClick: () => void;
      disabled?: boolean;
      className?: string;
    }
  | {
      type: "link";
      href: string;
      className?: string;
    };

export const TableEditButton = (props: GeneralProps) => {
  const defaultClasses =
    "leading-4.5 text-primary-500 hover:text-primary-600 flex cursor-pointer items-center gap-1 text-xs font-medium";
  if (props.type === "link") {
    return (
      <Link href={props.href} className={cn(defaultClasses, props.className)}>
        Edit
        <MdOutlineEdit />
      </Link>
    );
  }
  return (
    <button
      onClick={props.onClick}
      disabled={props.disabled}
      className={cn(defaultClasses, props.className)}
    >
      Edit
      <MdOutlineEdit />
    </button>
  );
};

export const TableViewButton = (props: GeneralProps) => {
  const defaultClasses =
    "leading-4.5 text-primary-500 hover:text-primary-600 flex cursor-pointer items-center gap-1 text-xs font-medium";
  if (props.type === "link") {
    return (
      <Link href={props.href} className={cn(defaultClasses, props.className)}>
        View
        <MdOutlineRemoveRedEye />
      </Link>
    );
  }
  return (
    <button
      disabled={props.disabled}
      onClick={props.onClick}
      className={cn(defaultClasses, props.className)}
    >
      View
      <MdOutlineRemoveRedEye />
    </button>
  );
};

type RemoveButtonProps = {
  onClick: () => void;
  label?: string;
  disabled?: boolean;
  className?: string;
};

export const TableRemoveButton = ({
  onClick,
  label = "Delete",
  disabled,
  className,
}: RemoveButtonProps) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "flex items-center gap-1 text-xs text-red-500 hover:text-red-600 disabled:cursor-not-allowed disabled:text-gray-300",
        className,
      )}
    >
      {label}
      <MdDelete className="h-4 w-4" />
    </button>
  );
};

type GenericButtonProps =
  | {
      type: "button";
      onClick: () => void;
      disabled?: boolean;
      className?: string;
      children: React.ReactNode;
    }
  | {
      type: "link";
      href: string;
      className?: string;
      children: React.ReactNode;
    };

export const TableGenericButton = (props: GenericButtonProps) => {
  const defaultClasses =
    "leading-4.5 text-primary-500 hover:text-primary-600 flex cursor-pointer items-center gap-1 text-xs font-medium";
  if (props.type === "link") {
    return (
      <Link href={props.href} className={cn(defaultClasses, props.className)}>
        {props.children}
      </Link>
    );
  }
  return (
    <button
      onClick={props.onClick}
      disabled={props.disabled}
      className={cn(defaultClasses, props.className)}
    >
      {props.children}
    </button>
  );
};
