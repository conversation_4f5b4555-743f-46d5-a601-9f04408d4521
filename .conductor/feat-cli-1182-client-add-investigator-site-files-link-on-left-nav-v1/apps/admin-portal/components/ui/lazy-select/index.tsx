"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import type {
  InfiniteData,
  UseInfiniteQueryResult,
} from "@tanstack/react-query";
import type { SelectProps as FlowbiteSelectProps } from "flowbite-react";
import { LegacyRef, useEffect, useMemo, useRef, useState } from "react";
import { Controller, get, useFormContext } from "react-hook-form";
import { MdOutlineClear } from "react-icons/md";

import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

type PageData<T> = {
  results: T[];
  metadata: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    itemsPerPage: number;
  };
};

export type LazySelectProps<T> = Omit<
  FlowbiteSelectProps,
  "name" | "onSelect"
> & {
  name: string;
  shouldShowError?: boolean;
  params?: any[];
  useInfiniteQuery: (
    search: string,
    ...args: any[]
  ) => UseInfiniteQueryResult<InfiniteData<PageData<T>>, unknown>;
  getOptionLabel: (option: T) => string;
  getOptionValue: (option: T) => string;
  mapData?: (data: T[]) => any[]; // New prop for transforming fetched data
  placeholder?: string;
  searchPlaceholder?: string;
  isDisabled?: boolean;
  readOnly?: boolean;
  onSelect?: (value: string) => void;
  dependentFieldNames?: string[];
  containerRef?: LegacyRef<HTMLDivElement>;
};

export const LazySelect = <T,>({
  name,
  shouldShowError = true,
  params = [],
  useInfiniteQuery,
  getOptionLabel,
  getOptionValue,
  mapData, // New prop to map data into options
  placeholder = "Select an option",
  searchPlaceholder = "Search...",
  isDisabled = false,
  readOnly = false,
  onSelect,
  className,
  dependentFieldNames,
  containerRef,
}: LazySelectProps<T>) => {
  const { control, watch, setValue } = useFormContext();
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search);
  const [isOpen, setIsOpen] = useState(false);
  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const value = watch(name);

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => {
      setIsOpen(open);
      setSearch("");
    },
    placement: "bottom-start",
    middleware: [
      offset(8),
      flip({
        fallbackPlacements: ["top-start"],
        fallbackStrategy: "bestFit",
        padding: 1,
        crossAxis: false,
      }),
      shift({
        padding: 1,
      }),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
          });
        },
        padding: 1,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context, { enabled: !readOnly }),
    useDismiss(context),
  ]);

  const { data, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useInfiniteQuery(debouncedSearch, ...params);

  const baseOptions = useMemo(() => {
    if (!data) return [];
    const results = data.pages.flatMap((page) => page.results);
    return mapData ? mapData(results) : results;
  }, [data, mapData]);

  const selectedOption = baseOptions.find(
    (opt) => getOptionValue(opt) === value,
  );

  const filteredOptions = useMemo(() => {
    return debouncedSearch
      ? baseOptions.filter((opt) =>
          getOptionLabel(opt)
            .toLowerCase()
            .includes(debouncedSearch.toLowerCase()),
        )
      : baseOptions;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [baseOptions, debouncedSearch]);

  useEffect(() => {
    if (!isOpen) return;

    const timeoutId = setTimeout(() => {
      const scrollContainer = scrollContainerRef.current;
      const target = observerTarget.current;

      if (!scrollContainer || !target) return;

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (
            entry.isIntersecting &&
            !isFetchingNextPage &&
            hasNextPage &&
            !debouncedSearch
          ) {
            fetchNextPage();
          }
        },
        {
          root: scrollContainer,
          rootMargin: "0px",
          threshold: 0.1,
        },
      );

      observer.observe(target);

      return () => {
        observer.disconnect();
      };
    }, 300);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, debouncedSearch, isOpen]);

  useEffect(() => {
    const { unsubscribe } = watch((_, { name: fieldName }) => {
      if (fieldName && dependentFieldNames?.includes(fieldName)) {
        setValue(name, "");
        setSearch("");
      }
    });
    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watch]);

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, formState: { errors } }) => {
        const errorObj = get(errors, name);
        const errorMessage = errorObj?.message?.valueOf();
        const hasError = typeof errorMessage === "string";

        return (
          <div
            className={cn(
              "relative",
              hasError && shouldShowError && "mb-5",
              className,
            )}
            ref={containerRef}
          >
            <div
              ref={refs.setReference}
              {...getReferenceProps()}
              className="h-full w-full"
            >
              <button
                type="button"
                className={cn(
                  "flex w-full items-center justify-between rounded-lg border bg-gray-50 px-2.5 py-2.5 text-left text-sm focus:outline-none focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50",
                  "dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",
                  isDisabled || field.disabled || readOnly
                    ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                    : "border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-600",
                  hasError
                    ? "border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500"
                    : "focus:ring-primary-500 dark:focus:ring-primary-500",
                )}
                disabled={isDisabled || field.disabled || readOnly}
              >
                <span
                  className={cn(
                    "truncate",
                    !selectedOption && "text-[#8d94a1]",
                  )}
                >
                  {selectedOption
                    ? getOptionLabel(selectedOption)
                    : placeholder}
                </span>

                {selectedOption && !readOnly ? (
                  <MdOutlineClear
                    role="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      field.onChange("");
                      onSelect?.("");
                      setIsOpen(false);
                      setSearch("");
                      field.onBlur();
                    }}
                    className="size-4"
                  />
                ) : (
                  <svg
                    className={cn(
                      "h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200 dark:text-gray-400",
                      isOpen ? "rotate-180 transform" : "",
                    )}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            </div>
            {hasError && shouldShowError && (
              <span className="text-sm text-red-500 dark:text-red-400">
                {errorMessage}
              </span>
            )}
            {isOpen && !readOnly && (
              <FloatingPortal>
                <FloatingFocusManager context={context} modal={false}>
                  <div
                    ref={refs.setFloating}
                    style={floatingStyles}
                    {...getFloatingProps()}
                    className="z-50 rounded-lg border border-gray-200 bg-white py-1 shadow-lg dark:border-gray-600 dark:bg-gray-700"
                  >
                    <div className="px-3 pb-2">
                      <input
                        type="text"
                        className="focus:border-primary-500 focus:ring-primary-500 w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-200 dark:placeholder-gray-400"
                        placeholder={searchPlaceholder}
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                      />
                    </div>
                    <div
                      ref={scrollContainerRef}
                      className="select-options max-h-60 overflow-y-auto"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center py-4">
                          <div className="border-t-primary-600 dark:border-t-primary-500 h-5 w-5 animate-spin rounded-full border-2 border-gray-300 dark:border-gray-600" />
                        </div>
                      ) : filteredOptions.length > 0 ? (
                        filteredOptions.map((option) => {
                          const isSelected =
                            field.value === getOptionValue(option);
                          return (
                            <button
                              key={getOptionValue(option)}
                              type="button"
                              className={cn(
                                "w-full px-3 py-2 text-left text-sm",
                                "hover:bg-gray-100 dark:hover:bg-gray-600",
                                isSelected
                                  ? "bg-primary-50 text-primary-600 dark:bg-primary-600/20 dark:text-primary-400"
                                  : "text-gray-900 dark:text-gray-200",
                              )}
                              onClick={() => {
                                const value = getOptionValue(option);
                                field.onChange(value);
                                onSelect?.(value);
                                setIsOpen(false);
                                setSearch("");
                                field.onBlur();
                              }}
                            >
                              <span className="block truncate">
                                {getOptionLabel(option)}
                              </span>
                            </button>
                          );
                        })
                      ) : (
                        <div className="flex items-center justify-center px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                          No options found
                        </div>
                      )}
                      <div
                        ref={observerTarget}
                        className="h-px"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                </FloatingFocusManager>
              </FloatingPortal>
            )}
          </div>
        );
      }}
    />
  );
};
