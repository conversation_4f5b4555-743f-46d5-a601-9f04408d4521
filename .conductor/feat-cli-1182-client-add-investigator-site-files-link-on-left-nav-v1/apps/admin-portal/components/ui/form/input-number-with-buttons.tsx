"use client";
import { TextInput } from "flowbite-react";
import { Minus, Plus } from "lucide-react";
import { ComponentPropsWithoutRef, ElementRef, forwardRef } from "react";
import { Controller, get, useFormContext } from "react-hook-form";
import { NumericFormat } from "react-number-format";

import { cn } from "@/lib/utils";

import { Button } from "../button";

const StyledInput = forwardRef<
  ElementRef<"input">,
  ComponentPropsWithoutRef<"input"> & {
    isError?: boolean;
  }
>(({ isError, ...props }, ref) => (
  <TextInput
    {...props}
    ref={ref}
    className={cn(
      "flex-1 [&_input]:text-center",
      isError && "[&_input]:!border-red-500 [&_input]:!ring-red-500",
      props.className,
    )}
  />
));

StyledInput.displayName = "StyledInput";

type Props = {
  name: string;
  isShowButtons?: boolean;
  isAllowNegative?: boolean;
  decimalScale?: number;
  min?: number;
  max?: number;
  step?: number;
  readOnly?: boolean;
} & ComponentPropsWithoutRef<"input">;

export const InputNumber = ({
  name,
  isShowButtons = false,
  isAllowNegative = true,
  decimalScale = 2,
  min,
  max,
  placeholder,
  id,
  step = 1,
  className,
  readOnly = false,
}: Props) => {
  const { control } = useFormContext();

  const allowNegative =
    typeof min === "number" && min >= 0 ? false : isAllowNegative;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { ref, ...rest }, formState: { errors } }) => {
        const errorObj = get(errors, name);
        const errorMessage = errorObj?.message?.valueOf();
        const isError = typeof errorMessage === "string";

        const currentValue = !rest.value
          ? min || 0
          : decimalScale
            ? +parseFloat(rest.value).toFixed(decimalScale)
            : parseInt(rest.value);

        const isDisableDecrease =
          typeof min === "number" && currentValue <= min;

        const isDisableIncrease =
          typeof max === "number" && currentValue >= max;

        const handleDecrement = () => {
          if (isDisableDecrease || readOnly) return;
          const newValue =
            typeof min === "number"
              ? Math.max(currentValue - step, min)
              : currentValue - step;
          rest.onChange(newValue);
        };
        const handleIncrease = () => {
          if (isDisableIncrease || readOnly) return;
          const newValue =
            typeof max === "number"
              ? Math.min(currentValue + step, max)
              : currentValue + step;
          rest.onChange(newValue);
        };

        return (
          <>
            <div className="flex w-full items-center gap-[6px]">
              {isShowButtons && (
                <Button
                  onClick={handleDecrement}
                  variant="outline"
                  className="!size-11 cursor-pointer"
                  disabled={isDisableDecrease || readOnly}
                  type="button"
                >
                  <Minus />
                </Button>
              )}
              <NumericFormat
                {...rest}
                onChange={(e) => {
                  const value = e.target.value;
                  rest.onChange(
                    value && typeof +value === "number" ? +value : undefined,
                  );
                }}
                onBlur={() => {
                  if (typeof max === "number" && currentValue > max) {
                    rest.onChange(max);
                  }
                  if (typeof min === "number" && currentValue < min) {
                    rest.onChange(min);
                  }
                  rest.onBlur();
                }}
                autoComplete="off"
                min={min}
                max={max}
                placeholder={placeholder}
                allowNegative={allowNegative}
                customInput={StyledInput}
                getInputRef={ref}
                decimalScale={decimalScale}
                id={id}
                inputMode="numeric"
                isError={isError}
                onKeyDown={(e) => {
                  if (readOnly) return;
                  if (e.key === "ArrowUp") {
                    handleIncrease();
                  }
                  if (e.key === "ArrowDown") {
                    handleDecrement();
                  }
                }}
                readOnly={readOnly}
                // isAllowed={(values) => {
                //   const value = values.floatValue;
                //   if (!value && value !== 0) {
                //     return true;
                //   }
                //   const withinMax = max === undefined || value <= max;
                //   const withinMin = min === undefined || value >= min;

                //   return withinMax && withinMin;
                // }}
                className={cn(
                  !isShowButtons && "[&_input]:text-left",
                  className,
                )}
              />
              {isShowButtons && (
                <Button
                  variant="outline"
                  type="button"
                  className="!size-11 cursor-pointer"
                  onClick={handleIncrease}
                  disabled={isDisableIncrease || readOnly}
                >
                  <Plus />
                </Button>
              )}
            </div>
            {isError && (
              <div className={cn("flex", isShowButtons && "justify-center")}>
                <span className="text-sm text-red-500">{errorMessage}</span>
              </div>
            )}
          </>
        );
      }}
    />
  );
};
