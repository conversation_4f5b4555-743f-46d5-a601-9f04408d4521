"use client";

import "react-day-picker/style.css";
import "./date-picker.css";

import { format as formatDate } from "date-fns";
import { Calendar, X } from "lucide-react";
import { useState } from "react";
import { DateRange, DayPicker } from "react-day-picker";
import { get, useController, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

import { Dropdown, DropdownContent, DropdownTrigger } from "../../dropdown";
import { CustomCaptionLabel } from "./custom-caption-label";

export const DATE_FORMATS = {
  "mm/dd/yyyy": "LL/dd/yyyy",
  "dd/mm/yyyy": "dd/LL/yyyy",
  "yyyy/mm/dd": "yyyy/LL/dd",
} as const;

type DateFormat = keyof typeof DATE_FORMATS;

type DateRangePickerProps = {
  name: string;
  placeholder?: string;
  disabled?: boolean;
  format?: DateFormat;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
};

const formatDateRangeValue = (format: DateFormat, range?: DateRange) => {
  if (!range?.from) return "";

  const fromString = formatDate(range.from, DATE_FORMATS[format]);

  const toString = formatDate(range.to || range?.from, DATE_FORMATS[format]);
  return `${fromString} - ${toString}`;
};

export const DateRangePicker = ({
  name,
  placeholder = "Select date range",
  disabled = false,
  format = "mm/dd/yyyy",
  minDate,
  maxDate,
  className,
  ...props
}: DateRangePickerProps) => {
  const { control } = useFormContext();
  const {
    field,
    formState: { errors },
  } = useController({ name, control, ...props });

  const [isOpen, setIsOpen] = useState(false);
  const [month, setMonth] = useState<Date>(field.value?.from || new Date());
  const [showYears, setShowYears] = useState(false);

  const isDisabled = disabled || field.disabled;

  const currentYear = month.getFullYear();

  const errorObj = get(errors, name);
  const errorMessage = errorObj?.message;
  const hasError = typeof errorMessage === "string";

  const displayValue = formatDateRangeValue(format, field.value);

  const handleRangeSelect = (range?: DateRange) => {
    field.onChange(range);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    field.onChange(undefined);
    field.onBlur();
  };

  const disabledRange = () => {
    if (minDate && maxDate) {
      return {
        after: maxDate,
        before: minDate,
      };
    }
    if (minDate) {
      return {
        before: minDate,
      };
    }
    if (maxDate) {
      return {
        after: maxDate,
      };
    }
    return isDisabled;
  };

  return (
    <>
      <Dropdown
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (!open) field.onBlur();
        }}
        placement="bottom-start"
      >
        <DropdownTrigger className="w-full">
          <div
            className={cn(
              "date-range-picker relative flex w-full items-center rounded-md border bg-gray-50 p-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:focus:ring-blue-400",
              hasError &&
                "border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500",
              isDisabled && "cursor-not-allowed opacity-50",
              className,
            )}
          >
            <input
              readOnly
              disabled={isDisabled}
              value={displayValue}
              placeholder={placeholder}
              className="flex-1 bg-transparent text-sm text-gray-900 placeholder:text-gray-400 focus:outline-none dark:text-white dark:placeholder:text-gray-500"
            />
            {field.value?.from ? (
              <button
                type="button"
                onClick={handleClear}
                disabled={isDisabled}
                className="ml-2 flex items-center justify-center rounded-full text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
              >
                <X className="size-4" />
              </button>
            ) : (
              <Calendar className="ml-2 size-4 text-gray-400 dark:text-gray-500" />
            )}
          </div>
        </DropdownTrigger>
        <DropdownContent className="rounded-xl dark:bg-[#1f2937]">
          <DayPicker
            mode="range"
            selected={field.value}
            onSelect={handleRangeSelect}
            month={month}
            onMonthChange={setMonth}
            className="w-fit p-3"
            formatters={{
              formatWeekdayName: (weekday) => formatDate(weekday, "EEEEE"),
            }}
            disabled={disabledRange()}
            components={{
              CaptionLabel: (props) => (
                <CustomCaptionLabel
                  showYears={showYears}
                  setShowYears={setShowYears}
                  currentYear={currentYear}
                  month={month}
                  setMonth={setMonth}
                  minDate={minDate}
                  maxDate={maxDate}
                >
                  {props.children}
                </CustomCaptionLabel>
              ),
            }}
            hideNavigation
          />
        </DropdownContent>
      </Dropdown>
      {hasError && (
        <span className="text-sm text-red-500 dark:text-red-400">
          {errorMessage}
        </span>
      )}
    </>
  );
};
