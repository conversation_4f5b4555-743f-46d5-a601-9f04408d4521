import { add, sub } from "date-fns";
import { ChevronDown, ChevronLeftIcon, ChevronRightIcon } from "lucide-react";

import { cn } from "@/lib/utils";

const scrollToSelected = (ref: HTMLUListElement | null, year: number) => {
  const selectedElement = ref?.querySelector(`[data-value="${year}"]`);
  selectedElement?.scrollIntoView({
    behavior: "instant",
    block: "center",
  });
};

const generateYears = (minDate?: Date, maxDate?: Date): number[] => {
  const minYear = minDate ? minDate.getFullYear() : 1900;
  const maxYear = maxDate ? maxDate.getFullYear() : 2100;
  return Array.from({ length: maxYear - minYear + 1 }, (_, i) => minYear + i);
};

type CustomCaptionLabelProps = {
  children: React.ReactNode;
  showYears: boolean;
  setShowYears: (show: boolean) => void;
  currentYear: number;
  month: Date;
  setMonth: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
};

export const CustomCaptionLabel = ({
  children,
  showYears,
  setShowYears,
  currentYear,
  month,
  setMonth,
  minDate,
  maxDate,
}: CustomCaptionLabelProps) => {
  const YEARS = generateYears(minDate, maxDate);
  const goToPreviousMonth = () => {
    setMonth(sub(month, { months: 1 }));
  };

  const goToNextMonth = () => {
    setMonth(add(month, { months: 1 }));
  };

  const isPreviousDisabled = minDate
    ? sub(month, { months: 1 }).getMonth() < minDate.getMonth()
    : false;

  const isNextDisabled = maxDate
    ? add(month, { months: 1 }).getMonth() > maxDate.getMonth()
    : false;

  const goToYear = (year: number) => {
    const newYear = month.setFullYear(year);
    setMonth(new Date(newYear));
  };

  return (
    <div className="flex flex-1 items-center justify-between">
      <button
        type="button"
        onClick={goToPreviousMonth}
        disabled={isPreviousDisabled}
        className={cn(
          "flex h-8 w-8 items-center justify-center rounded-lg opacity-100 transition-all hover:bg-gray-100 dark:hover:bg-gray-700",
          showYears && "invisible opacity-0",
          isPreviousDisabled &&
            "cursor-not-allowed opacity-50 hover:bg-transparent dark:hover:bg-transparent",
        )}
      >
        <ChevronLeftIcon className="h-4 w-4" />
      </button>

      <button
        onClick={() => setShowYears(!showYears)}
        className="flex items-center gap-1 rounded-lg px-2 py-1 text-base font-semibold transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
      >
        {children}
        <ChevronDown
          className={cn("transition-transform", showYears ? "rotate-180" : "")}
          size={15}
        />
      </button>

      <ul
        ref={(ref) => scrollToSelected(ref, currentYear)}
        className={cn(
          "year-dropdown-list invisible absolute inset-x-0 top-[55px] z-10 grid max-h-[calc(100%-55px)] flex-1 grid-cols-3 overflow-y-auto rounded-bl-xl rounded-br-xl bg-white opacity-0 transition-opacity dark:bg-[#1f2937]",
          showYears && "visible h-full opacity-100",
        )}
      >
        {YEARS.map((y) => (
          <li
            key={y}
            data-value={y}
            onClick={() => {
              goToYear(y);
              setShowYears(false);
            }}
            className={cn(
              "h-fit cursor-pointer rounded-lg px-3 py-2 text-center text-base text-gray-900 transition-colors hover:bg-gray-100 dark:text-gray-100 dark:hover:bg-gray-700",
              currentYear === y && "!bg-blue-100 dark:!bg-blue-900",
            )}
          >
            {y}
          </li>
        ))}
      </ul>

      <button
        type="button"
        onClick={goToNextMonth}
        disabled={isNextDisabled}
        className={cn(
          "flex h-8 w-8 items-center justify-center rounded-lg opacity-100 transition-all hover:bg-gray-100 dark:hover:bg-gray-700",
          showYears && "invisible opacity-0",
          isNextDisabled &&
            "cursor-not-allowed opacity-50 hover:bg-transparent dark:hover:bg-transparent",
        )}
      >
        <ChevronRightIcon className="h-4 w-4" />
      </button>
    </div>
  );
};
