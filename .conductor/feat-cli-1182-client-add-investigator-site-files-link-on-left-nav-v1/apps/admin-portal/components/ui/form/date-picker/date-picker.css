.rdp-root {
  @apply font-poppins;

  --rdp-day_button-height: 32px;
  --rdp-day_button-width: 32px;
  --rdp-day-height: 32px;
  --rdp-day-width: 32px;

  /* Light theme variables */
  --bg-primary: white;
  --text-primary: #000;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --hover-bg: #e5e7eb;
  --selected-bg: #003;
  --selected-text: white;
  --today-bg: #e5e7eb;
  --shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;

  padding: 12px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-radius: 12px;
  box-shadow: var(--shadow);
}

html.dark .rdp-root {
  --bg-primary: #1f2937;
  --text-primary: #e5e7eb;
  --text-secondary: #9ca3af;
  --border-color: #374151;
  --hover-bg: #374151;
  --selected-bg: #d1d5db;
  --selected-text: #003;
  --today-bg: #4b5563;
  --shadow: rgba(0, 0, 0, 0.3) 0px 7px 29px 0px;
  --rdp-range_middle-background-color: #374151;
}

.rdp-root[data-mode="range"] .rdp-day.rdp-today .rdp-day_button {
  background-color: transparent;
}

.rdp-nav {
  display: none;
}

.rdp-weekday,
.rdp-day_button {
  font-size: 14px;
  color: var(--text-primary);
}

.rdp-weekday {
  font-weight: 600;
  color: var(--text-secondary);
}

.rdp-day_button {
  border-radius: 8px;
  color: var(--text-primary);
  background-color: transparent;
  border: none;
}
.rdp-day_button:hover:not(.rdp-day_button:disabled) {
  background-color: var(--hover-bg);
  border: none;
}

.rdp-day.rdp-today .rdp-day_button {
  background-color: var(--today-bg);
  border: none;
}

.rdp-day.rdp-outside .rdp-day_button {
  color: var(--text-secondary);
  opacity: 0.5;
}

.rdp-day.rdp-selected:not(.rdp-range_middle) .rdp-day_button {
  color: var(--selected-text);
  background-color: var(--selected-bg) !important;
  font-weight: 500;
  border: none;
}

.rdp-day_button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.rdp-months {
  position: static;
}

html.dark .year-dropdown-list {
  scrollbar-color: #4b5563 #1e2a37;
}

html.dark .year-dropdown-list::-webkit-scrollbar-track {
  background: #1e2a37;
}

html.dark .year-dropdown-list::-webkit-scrollbar-thumb {
  background: #4b5563;
}

html.dark .year-dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
