"use client";
import { Popover } from "flowbite-react";
import { AlertCircle, Edit2 } from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import { get, useController, useFormContext } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { useOnClickOutside } from "usehooks-ts";

import { cn } from "@/lib/utils";

export type Props = {
  name: string;
  placeholderText?: string;
  className?: string;
  isAllowNegative?: boolean;
  d?: boolean;
  min?: number;
  max?: number;
  step?: number;
  decimalScale?: number;
};

const onFocus = (ref: HTMLInputElement | null) => {
  ref?.focus();
};

export const NumberCell = ({
  name,
  className,
  placeholderText = "Click to edit",
  isAllowNegative = true,
  decimalScale = 2,
  min,
  max,
  step = 1,
}: Props) => {
  const cellRef = useRef<ElementRef<"div">>(null);
  const { control } = useFormContext();
  const [isEditing, setIsEditing] = useState(false);

  const {
    field,
    formState: { errors },
  } = useController({
    name,
    control,
  });
  const errorObj = get(errors, name);
  const errorMessage = errorObj?.message?.valueOf();
  const hasError = typeof errorMessage === "string";

  const allowNegative =
    typeof min === "number" && min >= 0 ? false : isAllowNegative;

  const currentValue = !field.value
    ? 0
    : decimalScale
      ? +parseFloat(field.value).toFixed(decimalScale)
      : parseInt(field.value);

  const isDisableDecrease = typeof min === "number" && currentValue <= min;

  const isDisableIncrease = typeof max === "number" && currentValue >= max;

  const handleDecrement = () => {
    if (isDisableDecrease || field.disabled) return;
    const newValue =
      typeof min === "number"
        ? Math.max(currentValue - step, min)
        : currentValue - step;
    field.onChange(newValue);
  };

  const handleIncrease = () => {
    if (isDisableIncrease || field.disabled) return;
    const newValue =
      typeof max === "number"
        ? Math.min(currentValue + step, max)
        : currentValue + step;
    field.onChange(newValue);
  };

  useOnClickOutside(cellRef, () => {
    if (isEditing) {
      setIsEditing(false);
      field.onBlur();
    }
  });

  const handleCellClick = () => {
    if (field.disabled) return;
    setIsEditing(true);
  };

  const displayValue =
    field.value !== undefined && field.value !== null && field.value !== ""
      ? field.value.toString()
      : placeholderText;

  return (
    <div
      ref={cellRef}
      className={cn(
        "group/number relative flex w-full items-center gap-2 border border-gray-300 bg-white p-2 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600",
        isEditing && "border-blue-400 shadow-sm dark:border-blue-500",
        hasError && "border-red-500",
        field.disabled && "cursor-not-allowed opacity-50",
        className,
      )}
      onClick={handleCellClick}
    >
      {isEditing ? (
        <NumericFormat
          {...field}
          onChange={(e) => {
            const value = e.target.value;
            field.onChange(
              value && typeof +value === "number" ? +value : undefined,
            );
          }}
          min={min}
          max={max}
          allowNegative={allowNegative}
          getInputRef={onFocus}
          decimalScale={decimalScale}
          inputMode="numeric"
          onKeyDown={(e) => {
            if (field.disabled) return;
            if (e.key === "ArrowUp") {
              e.preventDefault();
              handleIncrease();
            }
            if (e.key === "ArrowDown") {
              e.preventDefault();
              handleDecrement();
            }
          }}
          isAllowed={(values) => {
            const value = values.floatValue;
            if (!value && value !== 0) {
              return true;
            }
            const withinMax = max === undefined || value <= max;
            const withinMin = min === undefined || value >= min;

            return withinMax && withinMin;
          }}
          className="w-full border-none bg-transparent p-0 text-gray-900 outline-none focus:ring-0 disabled:cursor-not-allowed disabled:opacity-50 dark:text-white"
        />
      ) : (
        <>
          <div
            className={cn(
              "truncate text-gray-900 dark:text-white",
              (field.value === undefined ||
                field.value === null ||
                field.value === "") &&
                "text-gray-500 dark:text-gray-400",
            )}
          >
            {displayValue}
          </div>
          <Edit2
            className={cn(
              "size-3 flex-shrink-0 text-gray-400 opacity-0 transition-opacity group-hover/number:opacity-100 dark:text-gray-500",
              field.disabled && "hidden",
            )}
          />
        </>
      )}

      {hasError && (
        <Popover
          placement="top"
          trigger="hover"
          content={
            <p className="px-2 py-1 text-red-600 dark:text-red-400">
              {errorMessage}
            </p>
          }
        >
          <AlertCircle className="ml-auto h-4 w-4 flex-shrink-0 cursor-help text-red-500" />
        </Popover>
      )}
    </div>
  );
};
