import type { TextareaProps as FlowbiteTextareaProps } from "flowbite-react";
import { Textarea as FlowbiteTextarea } from "flowbite-react";
import { forwardRef } from "react";
import { Controller, get, useFormContext } from "react-hook-form";
import type { SetRequired } from "type-fest";

import { cn } from "@/lib/utils";

export type TextareaProps = SetRequired<FlowbiteTextareaProps, "name"> & {
  shouldShowError?: boolean;
};

const Textarea: React.ForwardRefExoticComponent<
  TextareaProps & React.RefAttributes<HTMLTextAreaElement>
> = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ name, rows = 3, shouldShowError = true, ...props }, ref) => {
    const { control } = useFormContext();
    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorObj = get(errors, name);
          const errorMessage = errorObj?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <div>
              <FlowbiteTextarea
                rows={rows}
                {...props}
                {...field}
                ref={ref}
                className={cn(
                  props.className,
                  "p-2.5",
                  hasError && "!border-red-500 !ring-red-500",
                )}
              />
              {hasError && shouldShowError && (
                <span className="text-sm text-red-500">{errorMessage}</span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

Textarea.displayName = "Textarea";

export { Textarea };
