import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import { useQuery } from "@tanstack/react-query";
import { type ComponentProps, useMemo, useRef, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { MdOutlineClear } from "react-icons/md";

import { useDebounce } from "@/hooks/use-debounce";
import api from "@/lib/apis";
import { cn } from "@/lib/utils";

import { Select } from "./select";

export const SelectProvinces = ({ name }: ComponentProps<typeof Select>) => {
  const { data } = useQuery({
    queryKey: ["provinces"],
    queryFn: () => api.addresses.getStateProvinces(),
    staleTime: Infinity,
  });
  const { control } = useFormContext();
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search);
  const [isOpen, setIsOpen] = useState(false);
  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const filteredOptions = useMemo(
    () =>
      data?.filter((state) =>
        state.name.toLowerCase().includes(debouncedSearch.toLowerCase()),
      ) ?? [],
    [debouncedSearch, data],
  );

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement: "bottom-start",
    middleware: [
      offset(8),
      flip({
        fallbackPlacements: ["top-start"],
        fallbackStrategy: "bestFit",
        padding: 1,
        crossAxis: false,
      }),
      shift({
        padding: 1,
      }),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
          });
        },
        padding: 1,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context),
    useDismiss(context),
  ]);
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, formState: { errors } }) => {
        const errorMessage = errors[name]?.message?.valueOf();
        const hasError = typeof errorMessage === "string";
        const selectedId = field.value;
        const selectedOptionData = data?.find(
          (state) => state.id === selectedId,
        );

        return (
          <div className={cn("relative", hasError && "mb-5")}>
            <div
              ref={refs.setReference}
              {...getReferenceProps()}
              className="h-full w-full"
            >
              <button
                type="button"
                className={cn(
                  "flex w-full items-center justify-between rounded-lg border bg-gray-50 px-2.5 py-2.5 text-left text-sm focus:outline-none focus:ring-2",
                  "dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",
                  // isDisabled
                  //   ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                  //   : "border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-600",
                  hasError
                    ? "border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500"
                    : "focus:ring-primary-500 dark:focus:ring-primary-500",
                )}
                // disabled={isDisabled}
              >
                <span
                  className={cn(
                    "truncate",
                    !selectedOptionData && "text-[#8d94a1]",
                  )}
                >
                  {selectedOptionData?.name || "Select state/province"}
                </span>

                {selectedOptionData ? (
                  <MdOutlineClear
                    className="size-4"
                    onClick={(e) => {
                      e.stopPropagation();
                      field.onChange("");
                    }}
                  />
                ) : (
                  <svg
                    className={cn(
                      "h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200 dark:text-gray-400",
                      isOpen ? "rotate-180 transform" : "",
                    )}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            </div>
            {isOpen && (
              <FloatingPortal>
                <FloatingFocusManager context={context} modal={false}>
                  <div
                    ref={refs.setFloating}
                    style={floatingStyles}
                    {...getFloatingProps()}
                    className="z-50 rounded-lg border border-gray-200 bg-white py-1 shadow-lg dark:border-gray-600 dark:bg-gray-700"
                  >
                    <div className="px-3 pb-2">
                      <input
                        type="text"
                        className="focus:border-primary-500 focus:ring-primary-500 w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-200 dark:placeholder-gray-400"
                        placeholder="Search by name"
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                      />
                    </div>
                    <div
                      ref={scrollContainerRef}
                      className="select-options max-h-60 overflow-y-auto"
                    >
                      {filteredOptions.length > 0 ? (
                        filteredOptions.map((option) => {
                          const isSelected = field.value === option.id;
                          return (
                            <button
                              key={option.id}
                              type="button"
                              className={cn(
                                "w-full px-3 py-2 text-left text-sm",
                                "hover:bg-gray-100 dark:hover:bg-gray-600",
                                isSelected
                                  ? "bg-primary-50 text-primary-600 dark:bg-primary-600/20 dark:text-primary-400"
                                  : "text-gray-900 dark:text-gray-200",
                              )}
                              onClick={() => {
                                const value = option.id;
                                field.onChange(value);
                                setIsOpen(false);
                                setSearch("");
                              }}
                            >
                              <span className="block truncate">
                                {option.name}
                              </span>
                            </button>
                          );
                        })
                      ) : (
                        <div className="flex items-center justify-center px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                          No options found
                        </div>
                      )}
                      <div
                        ref={observerTarget}
                        className="h-px"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                </FloatingFocusManager>
              </FloatingPortal>
            )}
          </div>
        );
      }}
    />
  );
};
