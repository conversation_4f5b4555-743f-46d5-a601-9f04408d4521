import { Editor, EditorProps, Monaco, OnMount } from "@monaco-editor/react";
import { useController, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

import { Skeleton } from "../skeleton";

export type MonacoEditorInstance = Parameters<OnMount>[0];
// export type Monaco = Parameters<OnMount>[0];

type Props = EditorProps & {
  name: string;
  containerClassName?: string;
  shouldShowError?: boolean;
  isAllowFormat?: boolean;
  editorRef?: React.MutableRefObject<MonacoEditorInstance | null>;
  monacoRef?: React.MutableRefObject<Monaco | null>;
};

export const MonacoEditor = ({
  name,
  containerClassName,
  theme = "vs-dark",
  isAllowFormat = false,
  shouldShowError = true,
  editorRef,
  monacoRef,
  ...rest
}: Props) => {
  const { control } = useFormContext();
  const { field, formState } = useController({
    control,
    name,
  });
  const errorMessage = formState.errors?.[name]?.message;

  const options: EditorProps["options"] = {
    minimap: {
      enabled: false,
    },
    scrollbar: {
      alwaysConsumeMouseWheel: false,
    },
    tabSize: 2,
    wordWrap: "on",
    wordWrapColumn: 80,
    automaticLayout: true,
    ...rest.options,
  };

  return (
    <div className={cn(containerClassName)}>
      <Editor
        theme={theme}
        onMount={(editor, monaco) => {
          if (editorRef) {
            editorRef.current = editor;
          }
          if (monacoRef) {
            monacoRef.current = monaco;
          }

          editor.onDidBlurEditorText(field.onBlur);
          if (isAllowFormat) {
            const handler = editor.onDidChangeModelDecorations((_) => {
              handler.dispose();
              editor.getAction("editor.action.formatDocument")?.run();
            });
          }
        }}
        {...rest}
        onChange={field.onChange}
        value={field.value}
        options={options}
        loading={<Skeleton className="h-full w-full" />}
      />
      {!!errorMessage && shouldShowError && (
        <span className="text-sm text-red-500">{errorMessage.toString()}</span>
      )}
    </div>
  );
};
