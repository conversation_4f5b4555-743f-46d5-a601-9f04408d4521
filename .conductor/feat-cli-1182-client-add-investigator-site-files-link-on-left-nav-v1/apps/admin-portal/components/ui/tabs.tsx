"use client";
import type { TabsProps, TabsRef } from "flowbite-react";
import { Tabs as FlowbiteTabs } from "flowbite-react";

import { cn } from "@/lib/utils";

export const Tabs = (
  props: TabsProps & {
    ref?: React.MutableRefObject<TabsRef | null>;
  },
) => {
  return (
    <FlowbiteTabs
      ref={props.ref}
      {...props}
      className={cn(
        "[&>button[aria-selected=true]]:!border-primary-700 [&>button:focus]:!ring-0 [&>button[aria-selected=true]]:border-b-2",
        "[&>button[aria-selected=true]]:text-primary-700 [&>button]:!bg-inherit",
        "dark:[&>button[aria-selected=true]]:!border-primary-500 dark:[&>button[aria-selected=true]]:text-primary-500 ",
        props.className,
      )}
    />
  );
};

export const TabsItem = FlowbiteTabs.Item;
