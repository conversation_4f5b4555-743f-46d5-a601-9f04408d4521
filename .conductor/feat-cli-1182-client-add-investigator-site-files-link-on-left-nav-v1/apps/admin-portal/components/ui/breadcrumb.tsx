"use client";
import Link from "next/link";
import { MdDashboard, MdKeyboardArrowRight } from "react-icons/md";

import { Skeleton } from "./skeleton";

export type BreadcrumbItem = {
  label: string;
  href?: string;
  loading?: boolean;
  icon?: React.ReactNode;
};

type BreadcrumbProps = {
  items: BreadcrumbItem[];
  includeDashboard?: boolean;
};

export const Breadcrumb = ({
  items,
  includeDashboard = true,
}: BreadcrumbProps) => {
  return (
    <nav aria-label="Page breadcrumb">
      <ol className="flex items-center gap-x-2">
        {includeDashboard && (
          <li className="group flex cursor-pointer items-center">
            <Link
              className="flex items-center gap-2 truncate text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
              href="/dashboards"
            >
              <MdDashboard />
              Dashboard
            </Link>
          </li>
        )}
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          const isFirst = index === 0;
          const showIcon = isFirst && !includeDashboard && item.icon;

          if (item.loading) {
            return (
              <li key={`loading-${index}`}>
                <Skeleton className="h-4 w-24" />
              </li>
            );
          }

          if (isLast) {
            return (
              <li
                className="group flex cursor-default items-center gap-2 truncate text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                key={index}
              >
                <MdKeyboardArrowRight className="flex-shrink-0" />
                <span className="truncate">{item.label}</span>
              </li>
            );
          }

          return (
            <li key={`${item.label}-${index}`}>
              <Link
                className="flex cursor-pointer items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                href={item.href as string}
              >
                {showIcon ? item.icon : <MdKeyboardArrowRight />}
                <span className="truncate">{item.label}</span>
              </Link>
            </li>
          );
        })}
      </ol>
    </nav>
  );
};
