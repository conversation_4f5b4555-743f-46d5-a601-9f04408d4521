import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

export const DOCUMENT_STATUSES = [
  "placeholder",
  "draft",
  "submitted",
  "reviewed",
  "attention",
  "finalized",
  "rejected",
] as const;

export type DocumentStatus = (typeof DOCUMENT_STATUSES)[number];

const ebinderDocumentStatusVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block",
  {
    variants: {
      variant: {
        default:
          "!bg-gray-100 !text-gray-600 before:!bg-gray-600 dark:!bg-gray-800 dark:!text-gray-300 dark:before:!bg-gray-300",
        placeholder:
          "!bg-gray-100 !text-gray-600 before:!bg-gray-600 dark:!bg-gray-800 dark:!text-gray-300 dark:before:!bg-gray-300",
        draft:
          "!bg-teal-100 !text-teal-500 before:!bg-teal-500 dark:!bg-teal-900 dark:!text-teal-300 dark:before:!bg-teal-300",
        submitted:
          "bg-green-100 text-green-500 before:bg-green-500 dark:bg-green-900 dark:text-green-300 dark:before:bg-green-300",
        reviewed:
          "bg-blue-100 text-blue-500 before:bg-blue-500 dark:bg-blue-900 dark:text-blue-300 dark:before:bg-blue-300",
        attention:
          "bg-orange-100 text-orange-500 before:bg-orange-500 dark:bg-orange-900 dark:text-orange-300 dark:before:bg-orange-300",
        finalized:
          "bg-purple-50 text-purple-500 before:bg-purple-500 dark:bg-purple-900 dark:text-purple-300 dark:before:bg-purple-300",
        rejected:
          "bg-red-100 text-red-500 before:bg-red-500 dark:bg-red-900 dark:text-red-300 dark:before:bg-red-300",
      },
    },
    defaultVariants: {
      variant: "placeholder",
    },
  },
);

type EbinderDocumentStatusBadgeProps = {
  className?: string;
  status: DocumentStatus;
};

export const DocumentStatusBadge = ({
  className,
  status,
}: EbinderDocumentStatusBadgeProps) => {
  const validStatus = DOCUMENT_STATUSES.find((s) => s === status) || "default";
  return (
    <PillBadge
      className={cn(
        ebinderDocumentStatusVariants({ variant: validStatus }),
        "flex-shrink-0 gap-0 rounded-2xl capitalize",
        className,
      )}
    >
      {validStatus}
    </PillBadge>
  );
};
