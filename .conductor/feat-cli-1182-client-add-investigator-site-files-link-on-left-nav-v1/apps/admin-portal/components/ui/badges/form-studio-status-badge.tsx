import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

const badgeVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block capitalize",
  {
    variants: {
      variant: {
        DRAFT:
          "bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-300 before:bg-gray-500 dark:before:bg-gray-300",
        PUBLISHED:
          "bg-green-100 text-green-400 dark:bg-green-800 dark:text-green-200 before:bg-green-400 dark:before:bg-green-200",
        ARCHIVED:
          "bg-red-100 text-red-500 dark:bg-red-800 dark:text-red-200 before:bg-red-500 dark:before:bg-red-200",
      },
    },
    defaultVariants: {
      variant: "DRAFT",
    },
  },
);

type BadgeProps = {
  className?: string;
  variant: "DRAFT" | "PUBLISHED" | "ARCHIVED";
};

export const FormStudioStatusBadge = ({ className, variant }: BadgeProps) => {
  return (
    <PillBadge
      className={cn(badgeVariants({ variant }), "gap-0 rounded-2xl", className)}
    >
      {variant}
    </PillBadge>
  );
};
