import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { Badge as FlowbiteBadge } from "flowbite-react";

import { cn } from "@/lib/utils";

const activeStatusVariants = cva("w-fit text-xs font-normal", {
  variants: {
    variant: {
      active:
        "bg-green-100 text-green-800 dark:bg-green-600 dark:text-green-200",
      inactive: "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200",
    },
  },
  defaultVariants: {
    variant: "inactive",
  },
});

type ActiveStatusBadgeProps = VariantProps<typeof activeStatusVariants> & {
  className?: string;
  isActive: boolean;
};

export const ActiveStatusBadge = ({
  isActive,
  className,
}: ActiveStatusBadgeProps) => {
  const variant = isActive ? "active" : "inactive";
  const status = isActive ? "Active" : "Inactive";

  return (
    <FlowbiteBadge className={cn(activeStatusVariants({ variant }), className)}>
      {status}
    </FlowbiteBadge>
  );
};
