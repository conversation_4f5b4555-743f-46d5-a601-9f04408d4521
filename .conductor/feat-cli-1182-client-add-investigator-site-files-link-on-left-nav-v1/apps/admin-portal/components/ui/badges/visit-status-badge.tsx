import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

export type VisitStatus =
  | "Scheduled"
  | "Pending"
  | "In Progress"
  | "Cancelled"
  | "Completed";

const visitStatusVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block",
  {
    variants: {
      variant: {
        default:
          "bg-gray-100 text-gray-900 before:bg-gray-900 dark:bg-gray-700 dark:text-gray-100 dark:before:bg-gray-100",
        Scheduled:
          "bg-teal-100 text-teal-500 before:bg-teal-500 dark:bg-teal-700 dark:text-teal-200 dark:before:bg-teal-200",
        Pending:
          "bg-orange-100 text-orange-500 before:bg-orange-500 dark:bg-orange-700 dark:text-orange-200 dark:before:bg-orange-200",
        "In Progress":
          "bg-green-100 text-green-500 before:bg-green-500 dark:bg-green-700 dark:text-green-200 dark:before:bg-green-200",
        Cancelled:
          "bg-red-100 text-red-500 before:bg-red-500 dark:bg-red-700 dark:text-red-200 dark:before:bg-red-200",
        Completed:
          "bg-purple-50 text-purple-500 before:bg-purple-500 dark:bg-purple-700 dark:text-purple-200 dark:before:bg-purple-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type StatusProps = VariantProps<typeof visitStatusVariants> & {
  className?: string;
};

export const VisitStatusBadge = ({
  status,
  className,
}: {
  status: VisitStatus;
} & StatusProps) => {
  return (
    <PillBadge
      className={cn(
        visitStatusVariants({ variant: status }),
        "gap-0 rounded-2xl",
        className,
      )}
    >
      {status}
    </PillBadge>
  );
};
