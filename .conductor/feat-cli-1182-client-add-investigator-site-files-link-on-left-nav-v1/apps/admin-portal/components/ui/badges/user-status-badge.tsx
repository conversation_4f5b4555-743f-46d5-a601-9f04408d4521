import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { Badge as FlowbiteBadge } from "flowbite-react";

import { cn } from "@/lib/utils";

export enum UserStatus {
  ACTIVE = "active",
  DISABLED = "disabled",
  LOCKED = "locked",
  PASSWORD_RESET_REQUIRED = "password_reset_required",
}

export const userStatuses: Record<UserStatus, string> = {
  [UserStatus.ACTIVE]: "Active",
  [UserStatus.DISABLED]: "Disabled",
  [UserStatus.LOCKED]: "Locked",
  [UserStatus.PASSWORD_RESET_REQUIRED]: "Password Reset Required",
};

const badgeVariants = cva("w-fit text-xs font-normal", {
  variants: {
    variant: {
      active:
        "bg-green-100 text-green-800 dark:bg-green-600 dark:text-green-200",
      disabled: "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200",
      locked: "bg-red-100 text-red-800 dark:bg-red-600 dark:text-red-200",
      password_reset_required:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-600 dark:text-yellow-200",
    },
  },
  defaultVariants: {
    variant: "active",
  },
});

type BadgeProps = VariantProps<typeof badgeVariants> & {
  className?: string;
};

export const UserStatusBadge = ({
  status,
  className,
}: { status: string } & BadgeProps) => {
  const normalizedStatus = status.toLowerCase() as UserStatus;
  const displayStatus = userStatuses[normalizedStatus] || status;

  return (
    <FlowbiteBadge
      className={cn(
        badgeVariants({ variant: normalizedStatus }),
        className,
        "whitespace-nowrap",
      )}
    >
      {displayStatus}
    </FlowbiteBadge>
  );
};
