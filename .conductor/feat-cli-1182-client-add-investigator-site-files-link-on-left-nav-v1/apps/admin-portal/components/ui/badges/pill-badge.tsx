import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { Badge } from "flowbite-react";

import { cn } from "@/lib/utils";

const pillBadgeVariants = cva(
  "inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full whitespace-nowrap before:content-[''] before:block before:w-1.5 before:h-1.5 before:rounded-full",
  {
    variants: {
      variant: {
        default:
          "bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white before:bg-gray-500 dark:before:bg-gray-400",
        primary:
          "bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-200 before:bg-primary-500 dark:before:bg-primary-400",
        success:
          "bg-emerald-50 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-200 before:bg-emerald-500 dark:before:bg-emerald-400",
        warning:
          "bg-yellow-50 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-200 before:bg-yellow-500 dark:before:bg-yellow-400",
        danger:
          "bg-red-50 text-red-700 dark:bg-red-900 dark:text-red-200 before:bg-red-500 dark:before:bg-red-400",
        info: "bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200 before:bg-blue-500 dark:before:bg-blue-400",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type PillBadgeProps = VariantProps<typeof pillBadgeVariants> & {
  className?: string;
  children: React.ReactNode;
};

export const PillBadge = ({ className, variant, children }: PillBadgeProps) => {
  return (
    <Badge className={cn(pillBadgeVariants({ variant }), className)}>
      {children}
    </Badge>
  );
};
