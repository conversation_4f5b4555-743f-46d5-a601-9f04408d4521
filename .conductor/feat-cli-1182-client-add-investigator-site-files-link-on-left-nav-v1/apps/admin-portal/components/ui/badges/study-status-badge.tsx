import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { Badge as FlowbiteBadge } from "flowbite-react";

import type { StudyStatuses } from "@/lib/apis/studies";
import { cn } from "@/lib/utils";

export const studyStatuses: Record<StudyStatuses, string> = {
  preSelection: "Pre-Selection",
  startup: "Startup",
  enrollment: "Enrollment",
  followUp: "Follow-Up",
  closed: "Closed",
};

const badgeVariants = cva("w-fit text-xs font-normal", {
  variants: {
    variant: {
      preSelection:
        "bg-gray-100 text-gray-500 dark:bg-gray-600 dark:text-gray-300",
      startup:
        "bg-primary-100 text-primary-800 dark:bg-primary-600 dark:text-primary-200",
      enrollment:
        "bg-green-100 text-green-800 dark:bg-green-600 dark:text-green-200",
      followUp:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-600 dark:text-yellow-200",
      closed: "bg-red-100 text-red-800 dark:bg-red-600 dark:text-red-200",
    },
  },
  defaultVariants: {
    variant: "preSelection",
  },
});
type BadgeProps = VariantProps<typeof badgeVariants> & {
  className?: string;
};

export const StudyStatusBadge = ({
  status,
  className,
}: { status: string } & BadgeProps) => {
  return (
    <FlowbiteBadge
      className={cn(
        badgeVariants({ variant: status as StudyStatuses }),
        className,
      )}
    >
      {studyStatuses[status as StudyStatuses]}
    </FlowbiteBadge>
  );
};
