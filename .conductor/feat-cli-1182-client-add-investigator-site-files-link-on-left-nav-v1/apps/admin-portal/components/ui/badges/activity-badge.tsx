import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { PropsWithChildren } from "react";

import { cn } from "@/lib/utils";
import { snakeCaseToCapitalized } from "@/utils/string";

import { PillBadge } from "./pill-badge";

const activityBadgeVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap relative",
  {
    variants: {
      variant: {
        // Document & Version Lifecycle
        DOCUMENT_CREATED:
          "bg-emerald-50 text-emerald-600 before:bg-emerald-600 dark:bg-emerald-700/30 dark:text-emerald-300 dark:before:bg-emerald-300",
        DOCUMENT_VIEWED:
          "bg-blue-50 text-blue-600 before:bg-blue-600 dark:bg-blue-700/30 dark:text-blue-300 dark:before:bg-blue-300",
        DOCUMENT_DOWNLOADED:
          "bg-indigo-50 text-indigo-600 before:bg-indigo-600 dark:bg-indigo-700/30 dark:text-indigo-300 dark:before:bg-indigo-300",
        METADATA_UPDATED:
          "bg-amber-50 text-amber-600 before:bg-amber-600 dark:bg-amber-700/30 dark:text-amber-300 dark:before:bg-amber-300",
        VERSION_UPLOADED:
          "bg-purple-50 text-purple-600 before:bg-purple-600 dark:bg-purple-700/30 dark:text-purple-300 dark:before:bg-purple-300",
        DOCUMENT_DELETED:
          "bg-red-50 text-red-600 before:bg-red-600 dark:bg-red-700/30 dark:text-red-300 dark:before:bg-red-300",
        DOCUMENT_RESTORED:
          "bg-green-50 text-green-600 before:bg-green-600 dark:bg-green-700/30 dark:text-green-300 dark:before:bg-green-300",
        DOCUMENT_LISTED:
          "bg-slate-50 text-slate-600 before:bg-slate-600 dark:bg-slate-700/30 dark:text-slate-300 dark:before:bg-slate-300",
        DOCUMENT_WITHDRAWN:
          "bg-orange-50 text-orange-600 before:bg-orange-600 dark:bg-orange-700/30 dark:text-orange-300 dark:before:bg-orange-300",

        // Folders lifecycle
        FOLDER_CREATED:
          "bg-teal-50 text-teal-600 before:bg-teal-600 dark:bg-teal-700/30 dark:text-teal-300 dark:before:bg-teal-300",
        FOLDER_VIEWED:
          "bg-cyan-50 text-cyan-600 before:bg-cyan-600 dark:bg-cyan-700/30 dark:text-cyan-300 dark:before:bg-cyan-300",
        FOLDER_DELETED:
          "bg-rose-50 text-rose-600 before:bg-rose-600 dark:bg-rose-700/30 dark:text-rose-300 dark:before:bg-rose-300",
        FOLDER_UPDATED:
          "bg-yellow-50 text-yellow-600 before:bg-yellow-600 dark:bg-yellow-700/30 dark:text-yellow-300 dark:before:bg-yellow-300",

        // Consent lifecycle
        CONSENT_CREATED:
          "bg-violet-50 text-violet-600 before:bg-violet-600 dark:bg-violet-700/30 dark:text-violet-300 dark:before:bg-violet-300",
        CONSENT_VIEWED:
          "bg-sky-50 text-sky-600 before:bg-sky-600 dark:bg-sky-700/30 dark:text-sky-300 dark:before:bg-sky-300",
        CONSENT_UPDATED:
          "bg-lime-50 text-lime-600 before:bg-lime-600 dark:bg-lime-700/30 dark:text-lime-300 dark:before:bg-lime-300",
        CONSENT_LISTED:
          "bg-gray-50 text-gray-600 before:bg-gray-600 dark:bg-gray-700/30 dark:text-gray-300 dark:before:bg-gray-300",

        // Medical history
        MEDICAL_HISTORY_VIEWED:
          "bg-pink-50 text-pink-600 before:bg-pink-600 dark:bg-pink-700/30 dark:text-pink-300 dark:before:bg-pink-300",
        MEDICAL_HISTORY_CREATED:
          "bg-fuchsia-50 text-fuchsia-600 before:bg-fuchsia-600 dark:bg-fuchsia-700/30 dark:text-fuchsia-300 dark:before:bg-fuchsia-300",

        // Patient
        PATIENT_CREATED:
          "bg-emerald-50 text-emerald-700 before:bg-emerald-700 dark:bg-emerald-700/30 dark:text-emerald-300 dark:before:bg-emerald-300",
        PATIENT_LISTED:
          "bg-stone-50 text-stone-600 before:bg-stone-600 dark:bg-stone-700/30 dark:text-stone-300 dark:before:bg-stone-300",
        PATIENT_DELETED:
          "bg-red-50 text-red-700 before:bg-red-700 dark:bg-red-700/30 dark:text-red-300 dark:before:bg-red-300",
        PATIENT_VIEWED:
          "bg-blue-50 text-blue-700 before:bg-blue-700 dark:bg-blue-700/30 dark:text-blue-300 dark:before:bg-blue-300",
        PATIENT_UPDATED:
          "bg-orange-50 text-orange-700 before:bg-orange-700 dark:bg-orange-700/30 dark:text-orange-300 dark:before:bg-orange-300",

        // Studies
        STUDIES_LISTED:
          "bg-neutral-50 text-neutral-600 before:bg-neutral-600 dark:bg-neutral-700/30 dark:text-neutral-300 dark:before:bg-neutral-300",
        STUDIES_VIEWED:
          "bg-sky-50 text-sky-700 before:bg-sky-700 dark:bg-sky-700/30 dark:text-sky-300 dark:before:bg-sky-300",
        STUDIES_CREATED:
          "bg-green-50 text-green-700 before:bg-green-700 dark:bg-green-700/30 dark:text-green-300 dark:before:bg-green-300",
        STUDIES_UPDATED:
          "bg-amber-50 text-amber-700 before:bg-amber-700 dark:bg-amber-700/30 dark:text-amber-300 dark:before:bg-amber-300",

        // Workflow & Status Transitions
        STATUS_CHANGED:
          "bg-indigo-50 text-indigo-700 before:bg-indigo-700 dark:bg-indigo-700/30 dark:text-indigo-300 dark:before:bg-indigo-300",

        // Collaboration & Review
        COMMENT_ADDED:
          "bg-blue-50 text-blue-500 before:bg-blue-500 dark:bg-blue-700/30 dark:text-blue-300 dark:before:bg-blue-300",
        COMMENT_EDITED:
          "bg-yellow-50 text-yellow-600 before:bg-yellow-600 dark:bg-yellow-700/30 dark:text-yellow-300 dark:before:bg-yellow-300",
        COMMENT_DELETED:
          "bg-red-50 text-red-500 before:bg-red-500 dark:bg-red-700/30 dark:text-red-300 dark:before:bg-red-300",
        ANNOTATION_ADDED:
          "bg-purple-50 text-purple-500 before:bg-purple-500 dark:bg-purple-700/30 dark:text-purple-300 dark:before:bg-purple-300",
        REDACTION_ADDED:
          "bg-gray-50 text-gray-700 before:bg-gray-700 dark:bg-gray-700/30 dark:text-gray-300 dark:before:bg-gray-300",

        // Compliance & Electronic Signatures
        SIGNATURE_APPLIED:
          "bg-green-50 text-green-700 before:bg-green-700 dark:bg-green-700/30 dark:text-green-300 dark:before:bg-green-300",
        SIGNATURE_ADDED:
          "bg-emerald-50 text-emerald-700 before:bg-emerald-700 dark:bg-emerald-700/30 dark:text-emerald-300 dark:before:bg-emerald-300",
        SIGNATURE_REVOKED:
          "bg-red-50 text-red-700 before:bg-red-700 dark:bg-red-700/30 dark:text-red-300 dark:before:bg-red-300",
        AUDIT_TRAIL_EXPORTED:
          "bg-slate-50 text-slate-700 before:bg-slate-700 dark:bg-slate-700/30 dark:text-slate-300 dark:before:bg-slate-300",

        // Access Control & Permissions
        USER_ACCESS_GRANTED:
          "bg-green-50 text-green-600 before:bg-green-600 dark:bg-green-700/30 dark:text-green-300 dark:before:bg-green-300",
        USER_ACCESS_REVOKED:
          "bg-red-50 text-red-600 before:bg-red-600 dark:bg-red-700/30 dark:text-red-300 dark:before:bg-red-300",
        DOCUMENT_SHARED:
          "bg-blue-50 text-blue-600 before:bg-blue-600 dark:bg-blue-700/30 dark:text-blue-300 dark:before:bg-blue-300",

        // System & Automated Actions
        DOCUMENT_ARCHIVED:
          "bg-gray-50 text-gray-600 before:bg-gray-600 dark:bg-gray-700/30 dark:text-gray-300 dark:before:bg-gray-300",
        DOCUMENT_CLASSIFIED:
          "bg-violet-50 text-violet-600 before:bg-violet-600 dark:bg-violet-700/30 dark:text-violet-300 dark:before:bg-violet-300",
        LOGIN_SUCCESS:
          "bg-green-50 text-green-500 before:bg-green-500 dark:bg-green-700/30 dark:text-green-300 dark:before:bg-green-300",

        create:
          "bg-green-50 text-green-400 before:bg-green-400 dark:bg-green-950/30 dark:text-green-300 dark:before:bg-green-300",
        update:
          "bg-orange-50 text-orange-400 before:bg-orange-400 dark:bg-orange-950/30 dark:text-orange-300 dark:before:bg-orange-300",
        read: "bg-blue-50 text-blue-500 before:bg-blue-500 dark:bg-blue-950/30 dark:text-blue-400 dark:before:bg-blue-400",
        delete:
          "bg-red-50 text-red-400 before:bg-red-400 dark:bg-red-950/30 dark:text-red-300 dark:before:bg-red-300",
      },
    },
    defaultVariants: {
      variant: "read",
    },
  },
);

export type ActivityType = Exclude<
  VariantProps<typeof activityBadgeVariants>["variant"],
  undefined | null
>;

type ActivityBadgeProps = {
  className?: string;
  activity: ActivityType;
};

export const ActivityBadge = ({
  activity,
  className,
}: PropsWithChildren<ActivityBadgeProps>) => {
  return (
    <PillBadge
      className={cn(
        activityBadgeVariants({ variant: activity }),
        "rounded-2xl",
        className,
      )}
    >
      {snakeCaseToCapitalized(activity)}
    </PillBadge>
  );
};
