"use client";
import Link from "next/link";
import type { ReactNode } from "react";
import { MdArrowBack } from "react-icons/md";

import { cn } from "@/lib/utils";

type BasePageHeaderProps = {
  children: ReactNode;
  className?: string;
};

type Props =
  | (BasePageHeaderProps & {
      showBackButton?: true;
      href: string;
    })
  | (BasePageHeaderProps & {
      showBackButton?: false;
    });

export const PageHeader = (props: Props) => {
  if (!props.showBackButton) {
    return (
      <h1 className="text-xl font-semibold text-gray-900 lg:text-2xl xl:text-3xl dark:text-white">
        {props.children}
      </h1>
    );
  }
  return (
    <div className={cn("flex items-center gap-2 sm:gap-4", props.className)}>
      {props.showBackButton && (
        <Link
          href={props.href}
          className="flex size-8  flex-shrink-0 items-center justify-center rounded-full border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 sm:size-10 dark:border-gray-500 dark:text-gray-100 dark:hover:bg-gray-700"
        >
          <MdArrowBack size={16} />
        </Link>
      )}
      <h1 className="text-xl font-semibold text-gray-900 lg:text-2xl xl:text-3xl dark:text-white">
        {props.children}
      </h1>
    </div>
  );
};
