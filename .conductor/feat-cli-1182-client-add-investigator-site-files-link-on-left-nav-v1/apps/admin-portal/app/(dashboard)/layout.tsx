import { SignedIn, SignedOut } from "@clerk/nextjs";
import type { PropsWithChildren } from "react";

import { SDKInitializer } from "@/components/sdk-initializer";
import { useInactivity as UseInactive } from "@/hooks/use-inactive";

import {
  ClientAuthenticate,
  ClientSignInPageContent,
} from "./_client-components";
import { LayoutContent } from "./layout-content";
import { DashboardNavbar } from "./navbar";
import { DashboardSidebar } from "./sidebar";

export default async function DashboardLayout({ children }: PropsWithChildren) {
  return (
    <>
      <SignedIn>
        <ClientAuthenticate>
          <DashboardNavbar />
          <SDKInitializer />
          <div className="mt-16 flex items-start">
            <DashboardSidebar />
            <LayoutContent>{children}</LayoutContent>
          </div>
          <UseInactive />
        </ClientAuthenticate>
      </SignedIn>
      <SignedOut>
        <ClientSignInPageContent />
      </SignedOut>
    </>
  );
}
