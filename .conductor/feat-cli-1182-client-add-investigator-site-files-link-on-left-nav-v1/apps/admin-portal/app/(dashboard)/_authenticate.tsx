"use client";

import { useAuth } from "@clerk/nextjs";
import { useQueryClient } from "@tanstack/react-query";
import { usePathname, useRouter } from "next/navigation";
import type { PropsWithChildren } from "react";
import { useEffect } from "react";
import { AiOutlineLoading } from "react-icons/ai";
import { CiWarning } from "react-icons/ci";

import { Button } from "@/components/ui/button";
import { useAuthenticated } from "@/hooks/auth";
import { clearAllStores } from "@/utils/clear-stores";

export const Authenticate = ({ children }: PropsWithChildren) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const pathname = usePathname();
  const { data, isPending, isError, isFetched } = useAuthenticated();

  const { isLoaded, signOut } = useAuth();

  const userData = data?.data;

  useEffect(() => {
    const onAuthenticated = async () => {
      if (!isLoaded || !isFetched) return;

      if (isError || !userData || !userData?.currentProfile?.type) {
        await signOut({
          redirectUrl: "/authentication/sign-in",
        });
        return clearAllStores();
      }
      if (userData?.currentProfile?.type === "clincove" && pathname === "/") {
        router.replace("/dashboards");
      }
    };
    onAuthenticated();
  }, [userData, isError, isFetched, isLoaded, router, signOut]);

  const handleCancel = async () => {
    await signOut();
    clearAllStores();
    queryClient.clear();
    router.push("/authentication/sign-in");
  };

  const handleGoToClientPortal = async () => {
    let domain = window.location.origin;
    domain = domain.replace("-admin", "-app");
    await signOut({
      redirectUrl: domain,
    });
    queryClient.clear();
    clearAllStores();
  };

  if (!isLoaded || isPending) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <AiOutlineLoading className="h-10 w-10 animate-spin text-gray-900 dark:text-white" />
      </div>
    );
  }

  if (isLoaded && userData?.currentProfile?.type !== "clincove") {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-lg dark:bg-gray-800">
          <div className="mb-6 flex items-center justify-center">
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
              <CiWarning className="size-8 text-red-600 dark:text-red-400" />
            </div>
          </div>

          <h2 className="mb-4 text-center text-xl font-semibold text-gray-900 dark:text-white">
            Access Denied
          </h2>

          <p className="mb-8 text-center text-gray-600 dark:text-gray-300">
            You are trying to login to the Admin portal with the wrong account.
          </p>

          <div className="space-y-3">
            <Button
              className="w-full"
              variant="primary"
              onClick={handleGoToClientPortal}
            >
              Go to Client Portal
            </Button>

            <Button className="w-full" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return children;
};
