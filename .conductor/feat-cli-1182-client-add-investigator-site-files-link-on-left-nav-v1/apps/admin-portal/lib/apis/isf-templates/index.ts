import Base<PERSON>pi from "../base";
import { MetadataParams } from "../types";
import type {
  CreateFilePayload,
  CreateFolderPayload,
  CreateTemplateResponse,
  ImportTemplatePayload,
  ImportTemplateResponse,
  IsfTemplate,
  IsfTemplateFileResponse,
  IsfTemplateFolderResponse,
  ListTemplateResponse,
  MoveFilePayload,
  UpdateFilePayload,
  UpdateFolderPayload,
} from "./types";

class IsfTemplateApi extends BaseApi {
  constructor() {
    super("/isf-templates", true);
  }

  public async getTemplates(params: MetadataParams) {
    const query = params ? this.generateQueryParams(params) : "";
    return this.http.get<ListTemplateResponse>(`/?${query}`);
  }

  public async getTemplate(id: string) {
    return this.http.get<IsfTemplate>(`/${id}`);
  }

  public async createTemplate(payload: { name: string }) {
    return this.http.post<CreateTemplateResponse>(`/`, {
      name: payload.name,
    });
  }

  public async updateTemplate(payload: {
    id: string;
    name: string;
    isActive: boolean;
  }) {
    return this.http.patch<CreateTemplateResponse>(`/${payload.id}`, {
      name: payload.name,
      isActive: payload.isActive,
    });
  }

  public async deleteTemplate(id: string) {
    return this.http.delete(`/${id}`);
  }

  public async importTemplate(payload: ImportTemplatePayload) {
    return this.http.post<ImportTemplateResponse>(
      `/build/${payload.templateId}`,
      {
        studyId: payload.studyId,
        siteId: payload.siteId,
      },
    );
  }

  public async getFolders(id: string) {
    return this.http.get<IsfTemplateFolderResponse>(`/${id}/folders`);
  }

  public async createFolder(payload: CreateFolderPayload & { id: string }) {
    return this.http.patch<IsfTemplateFolderResponse>(
      `/${payload.id}/folders`,
      payload,
    );
  }
  public async moveFolder(payload: {
    folderName: string;
    newParentDirectoryName?: string;
    oldParentDirectoryName?: string;
    id: string;
  }) {
    return this.http.put<IsfTemplateFolderResponse>(
      `/${payload.id}/folders/move`,
      payload,
    );
  }

  public async updateFolder(payload: UpdateFolderPayload & { id: string }) {
    return this.http.put<IsfTemplateFolderResponse>(
      `/${payload.id}/folders`,
      payload,
    );
  }
  public async deleteFolder(payload: { id: string; folderName: string }) {
    return this.http.patch(`/${payload.id}/remove-folder`, {
      folderName: payload.folderName,
    });
  }

  public async getFiles({
    id,
    parentDirectoryName,
  }: {
    id: string;
    parentDirectoryName: string;
  }) {
    return this.http.get<IsfTemplateFileResponse>(`/${id}/files`, {
      parentDirectoryName,
    });
  }

  public async createFile(payload: CreateFilePayload & { id: string }) {
    return this.http.patch<IsfTemplateFileResponse>(
      `/${payload.id}/files`,
      payload,
    );
  }
  public async updateFile(payload: UpdateFilePayload & { id: string }) {
    return this.http.put<IsfTemplateFileResponse>(
      `/${payload.id}/files`,
      payload,
    );
  }

  public async moveFile(payload: MoveFilePayload & { id: string }) {
    return this.http.put<IsfTemplateFileResponse>(
      `/${payload.id}/files/move`,
      payload,
    );
  }

  public async deleteFile(payload: {
    id: string;
    title: string;
    parentDirectoryName: string;
  }) {
    return this.http.patch(`/${payload.id}/remove-file`, {
      title: payload.title,
      parentDirectoryName: payload.parentDirectoryName,
    });
  }
}

export const isfTemplates = new IsfTemplateApi();

export * from "./types";
