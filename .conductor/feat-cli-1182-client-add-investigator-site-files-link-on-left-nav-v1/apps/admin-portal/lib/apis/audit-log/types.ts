import { ListBaseResponse } from "../types";

export type AuditLogEntry = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  profileId: string;
  groupId: string;
  resourceId: string;
  summary: string;
  details: unknown;
  action: string;
  resourceType: string;
  profile: {
    name: string;
    userId: string;
  };
};

export type AuditLogListResponse = ListBaseResponse<AuditLogEntry>;

export type AuditLogAction = {
  key: string;
  title: string;
};

export type AuditLogResourceType = {
  key: string;
  title: string;
};
