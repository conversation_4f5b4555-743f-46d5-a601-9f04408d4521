import <PERSON><PERSON><PERSON> from "../base";
import type { MetadataParams } from "../types";
import type {
  CreateFieldPayload,
  FieldLibraryItem,
  FieldLibraryListResponse,
  UpdateFieldPayload,
} from "./types";

export class FieldLibraryApi extends BaseApi {
  constructor() {
    super("/field-library", true);
  }

  public async list(
    params?: MetadataParams,
  ): Promise<FieldLibraryListResponse> {
    return this.http.get<FieldLibraryListResponse>("", params);
  }

  public async get(id: string): Promise<FieldLibraryItem> {
    return this.http.get<FieldLibraryItem>(`/${id}`);
  }

  public async create(payload: CreateFieldPayload): Promise<FieldLibraryItem> {
    return this.http.post<FieldLibraryItem>("", payload);
  }

  public async update(payload: UpdateFieldPayload): Promise<FieldLibraryItem> {
    return this.http.put<FieldLibraryItem>(`/${payload.id}`, payload);
  }

  public async archive(id: string): Promise<FieldLibraryItem> {
    return this.http.patch<FieldLibraryItem>(`/${id}/archive`);
  }

  public async publish(id: string): Promise<FieldLibraryItem> {
    return this.http.patch<FieldLibraryItem>(`/${id}/publish`);
  }

  public async delete(id: string): Promise<void> {
    return this.http.delete<void>(`/${id}`);
  }

  public async createVersion(id: string): Promise<FieldLibraryItem> {
    return this.http.post<FieldLibraryItem>(`/${id}/create-version`);
  }
}
export const fieldLibrary = new FieldLibraryApi();
