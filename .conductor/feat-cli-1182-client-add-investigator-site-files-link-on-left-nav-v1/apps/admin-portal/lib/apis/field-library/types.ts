import { FIELD_TYPES } from "@/components/features/studies/study-detail/tabs/form-studio/fields/columns";
import type { ListBaseResponse } from "@/lib/apis/types";

export type FieldStatus = "DRAFT" | "PUBLISHED" | "ARCHIVED";

export type FieldConfig = {
  allowPartial?: string;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  maxLength?: number;
  min?: number;
  max?: number;
  unitOfMeasure?: string;
  layoutDirection?: "horizontal" | "vertical";
  decimalPlaces?: number;
  codeListId?: string;
  isDisplayOnForm?: boolean;
};

export type FieldLibraryItem = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  fieldName: string;
  fieldType: (typeof FIELD_TYPES)[number];
  displayLabel: string;
  shortCode: string;
  description?: string;
  status: FieldStatus;
  version: number;
  config?: FieldConfig;
  metadata?: any;
};

export type FieldLibraryListResponse = ListBaseResponse<FieldLibraryItem>;

export type CreateFieldPayload = {
  fieldName: string;
  studyId: string;
  fieldType: (typeof FIELD_TYPES)[number];
  displayLabel: string;
  description?: string;
  config?: FieldConfig;
  metadata?: any;
};

export type UpdateFieldPayload = CreateFieldPayload & {
  id: string;
};
