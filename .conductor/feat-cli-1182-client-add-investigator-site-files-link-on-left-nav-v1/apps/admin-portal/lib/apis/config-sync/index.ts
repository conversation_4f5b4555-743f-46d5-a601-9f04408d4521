import BaseApi from "../base";
import type {
  ConfigSyncRunResponse,
  ConfigSyncStatus,
  ConfigSyncUpdate,
  FetchByEntityParams,
  FetchByEntityResponse,
} from "./types";

class ConfigSyncApi extends BaseApi {
  constructor() {
    super("/config-sync", true);
  }

  public async getStatus(): Promise<ConfigSyncStatus> {
    return this.http.get<ConfigSyncStatus>("/status");
  }

  public async checkForUpdates(): Promise<ConfigSyncUpdate> {
    return this.http.get<ConfigSyncUpdate>("/check-for-updates");
  }

  public async run(): Promise<ConfigSyncRunResponse> {
    return this.http.post<ConfigSyncRunResponse>("/run", {});
  }

  public async fetchByEntity<T = any>(
    params: FetchByEntityParams,
  ): Promise<FetchByEntityResponse<T>> {
    return this.http.get<FetchByEntityResponse<T>>("/fetch-by-entity", params);
  }
}

export const configSync = new ConfigSyncApi();
export * from "./types";
