import <PERSON><PERSON><PERSON> from "../base";
import { MetadataParams } from "../types";
import type {
  CreateDocExchangeDocumentPayload,
  CreateDocExchangeFolderPayload,
  DocExchangeActivityResponse,
  DocExchangeDocument,
  DocExchangeDocumentResponse,
  DocExchangeFolder,
  EditDocExchangeDocumentPayload,
  MoveDocExchangeFolderPayload,
  SignedUploadVerifyDocExchangePayload,
  UpdateDocExchangeDocumentPayload,
  UpdateDocExchangeFolderPayload,
  UpdateStatusFilesPayload,
  UploadMultipleFilesPayload,
  UploadMultipleFilesResponse,
} from "./types";

class DocExchangeApi extends BaseApi {
  constructor() {
    super("/doc-exchange", true);
  }

  // Folder operations
  public async getFolders(params: MetadataParams = {}) {
    const url = this.generateQueryParams(params);
    return this.http.get<DocExchangeFolder[]>(`/folders?${url}`);
  }

  public async createFolder(payload: CreateDocExchangeFolderPayload) {
    return this.http.post<DocExchangeFolder>("/folders", payload);
  }

  public async updateFolder(
    folderId: string,
    payload: UpdateDocExchangeFolderPayload,
  ) {
    return this.http.patch<DocExchangeFolder>(`/folders/${folderId}`, payload);
  }

  public async moveFolder(
    payload: MoveDocExchangeFolderPayload & {
      folderId: string;
    },
  ) {
    return this.http.patch<DocExchangeFolder>(
      `/folders/${payload.folderId}/move`,
      payload,
    );
  }

  public async archiveFolder(folderId: string) {
    return this.http.put<DocExchangeFolder>(`/folders/${folderId}/archive`);
  }

  public async restoreFolder(folderId: string) {
    return this.http.put<DocExchangeFolder>(`/folders/${folderId}/un-archive`);
  }

  // Document operations
  public async getDocuments(params: MetadataParams = {}) {
    const url = params ? this.generateQueryParams(params) : "";
    return this.http.get<DocExchangeDocumentResponse>(`/folders/files?${url}`);
  }

  public async createDocument(payload: CreateDocExchangeDocumentPayload) {
    return this.http.post<DocExchangeDocument & { uploadUrl: string }>(
      "/files/signed-url-upload",
      payload,
    );
  }

  public async updateDocument(
    documentId: string,
    payload: UpdateDocExchangeDocumentPayload,
  ) {
    return this.http.put<DocExchangeDocument>(
      `/documents/${documentId}`,
      payload,
    );
  }

  // File operations
  public async verifyUpload(
    fileId: string,
    payload: SignedUploadVerifyDocExchangePayload,
  ) {
    return this.http.put<{ hmacVerified: boolean }>(
      `/files/${fileId}/verify-upload`,
      payload,
    );
  }

  public async signUrlDownload(fileId: string) {
    return this.http.get<{ url: string }>(
      `/files/${fileId}/signed-url-download`,
    );
  }

  public async getDocumentActivities(
    documentId: string,
    params: MetadataParams = {},
  ) {
    const url = this.generateQueryParams(params);
    return this.http.get<DocExchangeActivityResponse>(
      `/files/${documentId}/activities?${url}`,
    );
  }

  public async editDocument(
    payload: EditDocExchangeDocumentPayload & {
      documentId: string;
    },
  ) {
    return this.http.patch<DocExchangeDocument>(
      `/files/${payload.documentId}`,
      payload,
    );
  }

  public async archiveDocument(documentId: string) {
    return this.http.put<void>(`/files/${documentId}/archive`);
  }

  public async restoreDocument(documentId: string) {
    return this.http.put<void>(`/files/${documentId}/un-archive`);
  }

  public async uploadMultipleFiles(payload: UploadMultipleFilesPayload) {
    return this.http.post<UploadMultipleFilesResponse>(
      "/files/upload-multiple",
      payload,
    );
  }

  public async updateStatusFiles(payload: UpdateStatusFilesPayload) {
    return this.http.patch<void>("/files/status", payload);
  }

  public async signUrlUploadNewDocument(fileId: string) {
    return this.http.get<string>(`/files/${fileId}/signed-url-upload`);
  }
}

export const docExchange = new DocExchangeApi();
