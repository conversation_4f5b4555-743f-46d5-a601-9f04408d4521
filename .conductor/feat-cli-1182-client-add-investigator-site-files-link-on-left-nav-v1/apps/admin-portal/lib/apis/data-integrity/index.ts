import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  NoCategoryArtifactsResponse,
  NoTMFISFStudiesResponse,
  OrphanedRecordStatistic,
  PatientInactiveResponse,
  ProtocolNoStudyResponse,
  SiteNoAssignedUserResponse,
  StudyNoProtocolResponse,
  TaskInactiveResponse,
} from "./types";

export * from "./types";

class DataIntegrityApi extends BaseApi {
  constructor() {
    super("/data-integrity", true);
  }

  public async getOrphanedRecordStatistic() {
    return this.http.get<OrphanedRecordStatistic>(`/orphaned-record-statistic`);
  }

  public async getNoTMFISFStudies(params: MetadataParams = {}) {
    return this.http.get<NoTMFISFStudiesResponse>(
      `/no-tmf-isf-studies`,
      params,
    );
  }

  public async getStudiesNoProtocol(params: MetadataParams = {}) {
    return this.http.get<StudyNoProtocolResponse>(
      `/active-studies-no-protocol`,
      params,
    );
  }

  public async getSitesNoAssignedUser(params: MetadataParams = {}) {
    return this.http.get<SiteNoAssignedUserResponse>(
      `/active-sites-no-users`,
      params,
    );
  }

  public async getProtocolsNoStudy(params: MetadataParams = {}) {
    return this.http.get<ProtocolNoStudyResponse>(
      `/published-protocols-no-study`,
      params,
    );
  }

  public async getPatientsInactive(params: MetadataParams = {}) {
    return this.http.get<PatientInactiveResponse>(
      `/patients-in-inactive`,
      params,
    );
  }

  public async getTasksInactive(params: MetadataParams = {}) {
    return this.http.get<TaskInactiveResponse>(`/tasks-in-inactive`, params);
  }

  public async getNoCategoryArtifacts(params: MetadataParams = {}) {
    return this.http.get<NoCategoryArtifactsResponse>(
      `/no-category-artifacts`,
      params,
    );
  }
}

export const dataIntegrity = new DataIntegrityApi();
