import type { ListBaseResponse } from "../types";

export type AddISFRefModelPayload = {
  isfRefModel: string;
  description?: string;
};

export type UpdateISFRefModelPayload = AddISFRefModelPayload & {
  id: string;
};

export type ISFRefModel = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  isfRefModel: string;
  description?: string | null;
};

export type ISFRefModelListResponse = ListBaseResponse<ISFRefModel>;
