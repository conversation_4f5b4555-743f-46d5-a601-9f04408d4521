import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  AddPromptPayload,
  Prompt,
  PromptListResponse,
  UpdatePromptPayload,
} from "./types";

class PromptTemplateApi extends BaseApi {
  constructor() {
    super("/ai-prompt-template", true);
  }

  public async create(payload: AddPromptPayload) {
    return this.http.post<Prompt>("/", payload);
  }

  public async list(params?: MetadataParams) {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<PromptListResponse>(`?${paramUrl}`);
  }

  public async update(payload: UpdatePromptPayload) {
    return this.http.patch<Prompt>(`/${payload.id}`, payload);
  }

  public async activate(id: string) {
    return this.http.patch<Prompt>(`/${id}/activate`);
  }

  public async delete(id: string) {
    return this.http.delete(`/${id}`);
  }

  public async listModel(params?: { provider?: string }) {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<{
      results: string[];
    }>(`models?${paramUrl}`);
  }
}

export const promptTemplate = new PromptTemplateApi();
export * from "./types";
