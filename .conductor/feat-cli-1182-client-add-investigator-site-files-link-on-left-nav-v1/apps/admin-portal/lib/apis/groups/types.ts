import type { Address } from "../addresses";
import type { Assignment } from "../assignments";
import type { Site } from "../sites";
import type { Study } from "../studies";
import type { ListBaseResponse } from "../types";
import type { User } from "../users/types";

export type Group = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  phone: string | null;
  email: string | null;
  type: string | null;
  address: Address;
  groupInstitutionType: string | null;
  siteId: string | null;
  site: Site | null;
};

export type GroupAssignmentListResponse = ListBaseResponse<Assignment>;
export type GroupListResponse = ListBaseResponse<Group>;

export type AddGroupPayload = {
  name: string;
  phone: string;
  email: string;
  type: string;
  address: Omit<Address, "country" | "stateProvince">;
  groupInstitutionType: string;
  siteId?: string | null;
};

export type UpdateGroupPayload = AddGroupPayload;

export type Profile = {
  id: string;
  currentGroupId: string;
  name: string;
  roleId: string;
  userId: string;
  isActive: boolean;
  hasCustomPermissions: boolean;
  user: {
    firstName: string;
    lastName: string;
  };
  currentGroup: Group;
  rolesProfiles: RoleProfile[];
  isOnlyActiveProfile: boolean;
};

export type RoleProfile = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  roleId: string;
  profileId: string;
  role: Role;
};

export type Role = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  description: string;
  isActive: boolean;
  type: string;
};

export type GroupProfile = {
  id: string;
  profileId: string;
  profile: Profile;
  groupId: string;
  group: Group;
};

export type GroupStudy = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  groupId: string;
  siteId: string;
  studyId: string;
  site: Site;
  study: Study;
};

export type GroupProfileListResponse = ListBaseResponse<GroupProfile>;
export type GroupStudyListResponse = ListBaseResponse<GroupStudy>;

export type AddUserToGroupPayload = {
  groupId: string;
  userId: string;
  roleId: string;
};

export type RemoveUserFromGroupPayload = {
  groupId: string;
  profileId: string;
};

export type AddStudyToGroupPayload = {
  groupId: string;
  studyId: string;
  siteId: string;
};

export type RemoveStudyFromGroupPayload = AddStudyToGroupPayload;

export type EligibleUsersResponse = ListBaseResponse<User>;

export type UpdateGroupEntitlementsPayload = {
  entitlementIds: string[];
};
