import <PERSON><PERSON><PERSON> from "../base";
import { Entitlement } from "../entitlements";
import type { MetadataParams } from "../types";
import type {
  AddGroupPayload,
  AddStudyToGroupPayload,
  AddUserToGroupPayload,
  EligibleUsersResponse,
  Group,
  GroupAssignmentListResponse,
  GroupListResponse,
  GroupProfileListResponse,
  GroupStudyListResponse,
  RemoveStudyFromGroupPayload,
  RemoveUserFromGroupPayload,
  UpdateGroupEntitlementsPayload,
  UpdateGroupPayload,
} from "./types";

export class GroupsApi extends BaseApi {
  public constructor() {
    super("/groups", true);
  }

  public async list(params: MetadataParams): Promise<GroupListResponse> {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<GroupListResponse>(`?${paramUrl}`);
  }

  public async get(id: string) {
    return this.http.get<Group & { entitlements: Entitlement[] }>(`/${id}`);
  }

  public async update(id: string, data: UpdateGroupPayload): Promise<Group> {
    return this.http.put<Group>(`/${id}`, data);
  }

  public async create(data: AddGroupPayload): Promise<Group> {
    return this.http.post<Group>("/", data);
  }

  public async assignments(id: string): Promise<GroupAssignmentListResponse> {
    return this.http.get<GroupAssignmentListResponse>(`/${id}/assignments`);
  }

  public async profiles(id: string): Promise<GroupProfileListResponse> {
    return this.http.get<GroupProfileListResponse>(`/${id}/profiles`);
  }

  public async addProfileToGroup(payload: AddUserToGroupPayload) {
    return this.http.post(`/${payload.groupId}/profiles`, payload);
  }

  public async removeProfileFromGroup(payload: RemoveUserFromGroupPayload) {
    return this.http.delete(
      `/${payload.groupId}/profiles/${payload.profileId}`,
    );
  }

  public async studies(
    id: string,
    params?: MetadataParams,
  ): Promise<GroupStudyListResponse> {
    const query = params ? this.generateQueryParams(params) : "";
    return this.http.get<GroupStudyListResponse>(`/${id}/studies?${query}`);
  }

  public async addStudyToGroup(payload: AddStudyToGroupPayload) {
    return this.http.post(`/${payload.groupId}/studies`, payload);
  }

  public async removeStudyFromGroup(payload: RemoveStudyFromGroupPayload) {
    return this.http.delete(
      `/${payload.groupId}/studies/${payload.studyId}/sites/${payload.siteId}`,
    );
  }

  public async getEligibleUsers(
    groupId: string,
    params?: MetadataParams,
  ): Promise<EligibleUsersResponse> {
    const query = params ? this.generateQueryParams(params) : "";
    return this.http.get<EligibleUsersResponse>(
      `/${groupId}/eligible-users?${query}`,
    );
  }

  public async updateEntitlements(
    id: string,
    payload: UpdateGroupEntitlementsPayload,
  ): Promise<void> {
    return this.http.patch<void>(`/${id}/entitlements`, payload);
  }
}
