import BaseApi from "../base";
import type { MetadataParams } from "../types";
import {
  PatientVisitDocumentListResponse,
  ReOrderVisitActivities,
} from "./types";

class PatientVisitsApi extends BaseApi {
  constructor() {
    super("/patient-visits", true);
  }

  public async patientVisitDocuments(
    patientVisitId: string,
    params: MetadataParams = {},
  ) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<PatientVisitDocumentListResponse>(
      `/${patientVisitId}/documents?${paramUrl}`,
    );
  }

  public async reorderVisitActivities(
    visitId: string,
    activities: ReOrderVisitActivities,
  ) {
    return this.http.post(`/${visitId}/patient-activities/reorder`, activities);
  }
}

export const patientVisits = new PatientVisitsApi();

export * from "./types";
