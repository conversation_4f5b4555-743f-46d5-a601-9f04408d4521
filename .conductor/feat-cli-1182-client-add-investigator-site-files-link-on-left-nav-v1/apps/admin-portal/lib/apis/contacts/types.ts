import type { Address, CreateAddressPayload } from "../addresses";
import type { ListBaseResponse } from "../types";

export type Contact = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: Address;
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  role: string | null;
};

export type CreateContactPayload = {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: CreateAddressPayload;
  role?: string;
};

export type UpdateContactPayload = Partial<CreateContactPayload>;

export type ListContactsResponse = ListBaseResponse<Contact>;
