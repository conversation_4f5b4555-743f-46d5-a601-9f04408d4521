import BaseApi from "../base";
import type { ValidStatesResponse } from "./types";

/**
 * API service for artifact status operations
 */
class ArtifactStatusApi extends BaseApi {
  constructor() {
    super("/artifact-status", true);
  }

  /**
   * Get valid states for a document
   * @param documentId - The ID of the document
   * @returns An array of valid states for the document
   */
  public async getValidStates(documentId: string) {
    return this.http.get<ValidStatesResponse>(`/${documentId}/valid-states`);
  }
}

export const artifactStatus = new ArtifactStatusApi();
export * from "./types";
