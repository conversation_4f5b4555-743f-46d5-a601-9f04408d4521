export type CreateAddressPayload = {
  addressLine: string;
  city: string;
  stateProvinceId: string;
  zipPostalCode: string;
  countryId: string;
};

export type StateProvince = {
  id: string;
  code: string;
  name: string;
};

export type Country = {
  id: string;
  code: string;
  name: string;
};

export type Address = {
  addressLine: string;
  city: string;
  stateProvinceId: string;
  zipPostalCode: string;
  countryId: string;
  country: Country;
  stateProvince: StateProvince;
};
