import BaseApi from "../base";
import {
  AddCategorySummaryPayload,
  CategorySummaryResponse,
  UpdateCategorySummaryPayload,
} from "./types";

class CategoryAiSummaryApi extends BaseApi {
  constructor() {
    super("/category-ai-summary", true);
  }

  public async create(payload: AddCategorySummaryPayload) {
    return this.http.post<CategorySummaryResponse>(``, payload);
  }

  public async update(payload: UpdateCategorySummaryPayload) {
    return this.http.patch<CategorySummaryResponse>(`/${payload.id}`, payload);
  }
}

export const categoryAiSummary = new CategoryAiSummaryApi();
export * from "./types";
