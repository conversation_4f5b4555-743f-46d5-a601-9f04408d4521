import { DocumentType } from "@/components/icons/doc-icons";

import { FileRecord } from "../../models/file-record";

export type PatientVisitDocument = {
  id: string;
  createdDate: string;
  category: string;
  lastUpdatedDate: string;
  title: string;
  description?: string;
  fileRecordId: string;
  fileRecord?: FileRecord;
  status: string;
  processingStatus: string;
  isActive: boolean;
  archived: boolean;
  summary: string;
  tags: string[];
  type: string;
};

export type EssentialDocument = PatientVisitDocument;

export type SignedUrlResponse = {
  uploadUrl: string;
  currentDocument: EssentialDocument;
};

export const DOCUMENT_STATUS = {
  approved: "Approved",
  pendingReview: "Pending Review",
  inProgress: "In Progress",
  submitted: "Submitted",
  qc1: "QC1",
  qc2: "QC2",
  finalized: "Finalized",
  rejected: "Rejected",
};

export type SignedUrlPayload = {
  fileType: string;
  originationType: string;
  extension: string;
  binder: {
    title: string;
    description?: string;
    categoryId: string;
    documentTypeId: string;
    summary?: string;
    status: string;
    assignedProfileId: string;
    parentDirectoryId?: string;
  };
};

export type VerifyUploadPayload = {
  hmac: string;
};
