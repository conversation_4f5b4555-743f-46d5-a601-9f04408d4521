import BaseApi from "../base";
import type {
  SignedUrlPayload,
  SignedUrlResponse,
  VerifyUploadPayload,
} from "./types";

class EssentialDocumentsApi extends BaseApi {
  constructor() {
    super("/essential-document-versions", true);
  }

  public async signedUrlUpload(
    studyId: string,
    siteId: string,
    params: SignedUrlPayload,
  ) {
    return this.http.post<SignedUrlResponse>(
      `/study/${studyId}/site/${siteId}/signed-url-upload`,
      params,
    );
  }

  public async signUrlForBinder(
    binderId: string,
    payload: Omit<SignedUrlPayload, "binder">,
  ) {
    return this.http.post<SignedUrlResponse>(
      `/binder/${binderId}/signed-url-upload`,
      payload,
    );
  }

  public async verifyUpload(documentId: string, params: VerifyUploadPayload) {
    return this.http.put<void>(`/${documentId}/verify-upload`, params);
  }

  public async signUrlDownload(documentId: string) {
    return this.http.get<{ url: string }>(`/${documentId}/signed-url-download`);
  }
}

export const essentialDocuments = new EssentialDocumentsApi();

export * from "./types";
