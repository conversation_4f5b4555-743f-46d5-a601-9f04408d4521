"use client";

import { Send } from "lucide-react";

import { EntitlementGate } from "@/components/shared/entitlement/entitlement-gate";
import { PermissionGate } from "@/components/shared/permission/permission-gate";
import { useRole } from "@/hooks/auth";
import { cn } from "@/lib/utils";
import { usePatientStore } from "@/stores/patient-store";
import { useStudyStore } from "@/stores/study-store";
import { Pages, useActivePage } from "@/stores/use-active-page";

import { Link } from "../shared/link";
import { Accordion, AccordionContent, AccordionItem } from "../ui/accordion";
import { EBinderIcon } from "./icons/ebinder";
import { EdcIcon } from "./icons/edc";
import { FavoritesIcon } from "./icons/favorites";
import { IsfIcon } from "./icons/isf";
import { PatientsIcon } from "./icons/patients";
import { ProtocolExplorerIcon } from "./icons/protocol-explorer";
import { SmrIcon } from "./icons/smr";
import { StudySitesIcon } from "./icons/study-sites";
import { TasksIcon } from "./icons/tasks";
import { TmfIcon } from "./icons/tmf";
import { SidebarItem } from "./sidebar-item";

export type SidebarItem = {
  icon: React.ReactNode;
  label: string;
  href: string;
  items?: SidebarItem[];
  isDisabled?: boolean;
};

type SidebarItemsProps = {
  isCollapsed?: boolean;
};

export const SidebarItems = ({ isCollapsed }: SidebarItemsProps) => {
  const { currentStudy } = useStudyStore();
  const { currentPatient } = usePatientStore();
  const currentActivePage = useActivePage();

  const { is } = useRole();
  const isCro = is("cro");

  // Auto-open the accordion if any patient-related pages are active
  const isPatientPageActive =
    currentActivePage === Pages.StudyPatients ||
    currentActivePage === Pages.StudyPatientDetail ||
    currentActivePage === Pages.ElectricDataCapture ||
    currentActivePage === Pages.SourceMedicalRecords;

  return (
    <div className="flex flex-col">
      {isCro && (
        <SidebarItem
          icon={<StudySitesIcon />}
          label="Study Sites"
          href={`/studies/${currentStudy?.id}/sites`}
          isCollapsed={isCollapsed}
          expectedActivePage={Pages.StudySites}
        />
      )}

      <PermissionGate action="menu" subject="patients">
        <Accordion value={isPatientPageActive ? "study-patients" : ""}>
          <AccordionItem value="study-patients">
            <SidebarItem
              icon={<PatientsIcon />}
              label="Study Patients"
              href={`/studies/${currentStudy?.id}/patients`}
              isCollapsed={isCollapsed}
              expectedActivePage={Pages.StudyPatients}
              isAccordion={!!currentPatient}
            />

            <AccordionContent
              className={cn("p-0", !currentPatient && "hidden")}
            >
              {currentPatient ? (
                <div className="border-y border-gray-200 bg-gray-50">
                  <Link
                    href={`/studies/${currentStudy?.id}/patients/${currentPatient?.id}`}
                    className={cn(
                      "block px-6 py-3 !pl-14 text-xl font-bold leading-6",
                      currentActivePage === Pages.StudyPatientDetail &&
                        "text-purple-500",
                    )}
                  >
                    {currentPatient?.name}
                  </Link>
                  <EntitlementGate entitlement="EDC_CORE">
                    <SidebarItem
                      icon={<EdcIcon />}
                      label="Electronic Data Capture"
                      href={
                        isCro
                          ? `/studies/${currentStudy?.id}/patients/${currentPatient?.id}/forms-validation`
                          : `/studies/${currentStudy?.id}/patients/${currentPatient?.id}/edc`
                      }
                      isCollapsed={isCollapsed}
                      expectedActivePage={Pages.ElectricDataCapture}
                      className="!pl-14"
                    />
                  </EntitlementGate>

                  <EntitlementGate entitlement="SMR_CORE">
                    <PermissionGate action="menu" subject="sourceDocuments">
                      <SidebarItem
                        icon={<SmrIcon />}
                        label="Source Medical Records"
                        href={`/studies/${currentStudy?.id}/patients/${currentPatient?.id}/ctmr`}
                        isCollapsed={isCollapsed}
                        expectedActivePage={Pages.SourceMedicalRecords}
                        className="!pl-14"
                      />
                    </PermissionGate>
                  </EntitlementGate>
                </div>
              ) : (
                <div className="border-y border-gray-200 bg-gray-50 py-6 pl-14 text-gray-400">
                  No patient selected
                </div>
              )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </PermissionGate>

      <PermissionGate action="menu" subject="tasks">
        <SidebarItem
          icon={<TasksIcon />}
          label="Study Tasks"
          href={`/tasks?studyId=${currentStudy?.id}`}
          isCollapsed={isCollapsed}
          expectedActivePage={Pages.StudyTasks}
        />
      </PermissionGate>

      {isCro ? (
        <EntitlementGate entitlement="TMF_CORE">
          <PermissionGate action="menu" subject="tmf">
            <Accordion>
              <AccordionItem value="trial-master-file">
                <SidebarItem
                  icon={<TmfIcon />}
                  label="Trial Master File"
                  href={`/studies/${currentStudy?.id}/tmf/dashboard`}
                  isCollapsed={isCollapsed}
                  expectedActivePage={Pages.TMF}
                  isAccordion
                />

                <AccordionContent className="p-0">
                  <div className="border-y border-gray-200 bg-gray-50">
                    <PermissionGate action="menu" subject="tmf">
                      <SidebarItem
                        icon={<EBinderIcon />}
                        label="TMF eBinder"
                        href={`/studies/${currentStudy?.id}/tmf/documents`}
                        isCollapsed={isCollapsed}
                        expectedActivePage={Pages.TMFeBinder}
                        className="!pl-14"
                      />
                    </PermissionGate>
                    <PermissionGate action="read:own" subject="tmf">
                      <SidebarItem
                        icon={
                          <FavoritesIcon className="group-[.active]:fill-purple-500" />
                        }
                        label="My Favorites"
                        href={`/studies/${currentStudy?.id}/tmf/favorites`}
                        isCollapsed={isCollapsed}
                        expectedActivePage={Pages.TMFMyFavorites}
                        className="!pl-14"
                      />
                    </PermissionGate>
                    <PermissionGate action="menu" subject="tmf">
                      <SidebarItem
                        icon={<Send size={20} />}
                        label="Push to ISF"
                        href={`/studies/${currentStudy?.id}/tmf/push-to-isf`}
                        isCollapsed={isCollapsed}
                        expectedActivePage={Pages.PushToISF}
                        className="!pl-14"
                      />
                    </PermissionGate>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </PermissionGate>
        </EntitlementGate>
      ) : (
        <EntitlementGate entitlement="ISF_CORE">
          <PermissionGate action="menu" subject="isf">
            <Accordion>
              <AccordionItem value="investigator-site-file">
                <SidebarItem
                  icon={<IsfIcon />}
                  label="Investigator Site Files"
                  href={`/studies/${currentStudy?.id}/isf/dashboard`}
                  isCollapsed={isCollapsed}
                  expectedActivePage={Pages.ISF}
                  isAccordion
                />

                <AccordionContent className="p-0">
                  <div className="border-y border-gray-200 bg-gray-50">
                    <SidebarItem
                      icon={<EBinderIcon />}
                      label="eBinder"
                      href={`/studies/${currentStudy?.id}/isf/documents`}
                      isCollapsed={isCollapsed}
                      expectedActivePage={Pages.EBinder}
                      className="!pl-14"
                    />
                    <PermissionGate action="read:own" subject="isf">
                      <SidebarItem
                        icon={
                          <FavoritesIcon className="group-[.active]:fill-purple-500" />
                        }
                        label="My Favorites"
                        href={`/studies/${currentStudy?.id}/isf/favorites`}
                        isCollapsed={isCollapsed}
                        expectedActivePage={Pages.MyFavorites}
                        className="!pl-14"
                      />
                    </PermissionGate>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </PermissionGate>
        </EntitlementGate>
      )}

      {isCro && (
        <SidebarItem
          icon={<StudySitesIcon />}
          label="Investigator Site Files"
          href={`/studies/${currentStudy?.id}/sites`}
          isCollapsed={isCollapsed}
          expectedActivePage={Pages.StudySites}
        />
      )}

      <SidebarItem
        icon={<ProtocolExplorerIcon />}
        label="Protocol AI"
        href={`/protocol-explorer?studyId=${currentStudy?.id}`}
        isCollapsed={isCollapsed}
        expectedActivePage={Pages.ProtocolExplorer}
      />
    </div>
  );
};
