name: Release Pipeline

permissions:
  id-token: write
  contents: write
  pull-requests: write
  actions: read
  packages: read

on:
  push:
    branches:
      - develop
      - main
    paths-ignore:
      - '.github/**'
  pull_request:
    types: [opened, synchronize, reopened]
    paths-ignore:
      - '.github/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check:
    runs-on: ubuntu-latest
    if: ${{ github.triggering_actor != 'renovate[bot]' }}
    environment:
      name: ${{ github.ref_name == 'develop' && 'development' || 'development' }} # TODO: Enable production push
    timeout-minutes: 20
    steps:
      - uses: googleapis/release-please-action@d1a8f221d7723166f48a584aebba00ef3f6febec # v4.1.4
        id: release
        with:
          target-branch: ${{ github.base_ref || github.ref_name }}
          config-file: ${{ github.ref_name == 'main' && 'release-please-config.json' || 'release-please-develop-config.json' }}

      - name: "[DEBUGGING] release-please"
        if: ${{ true }}
        run: |
          echo "$RELEASE_PLEASE_OUTPUT"
        env:
          RELEASE_PLEASE_OUTPUT: ${{ toJson(steps.release.outputs) }}

      # Check out the tag ref "refs/tags/v1.2.3" if a release tag was just created.
      # Later the docker meta action will derive image tags from that.
      #
      - name: "[Checkout] Checkout code from tag"
        if: ${{ steps.release.outputs.release_created }}
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ steps.release.outputs.tag_name }}
          fetch-depth: 0
      - name: "[Checkout] Checkout code from workflow"
        if: ${{ ! steps.release.outputs.release_created }}
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      
      - uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: 'develop' # TODO: Enable 'main' target
      - name: Install pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: 10
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
          cache: 'pnpm'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@clincove-eng'

      # ------------------------------------------------
      - run: pnpm i --frozen-lockfile
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - run: pnpm nx affected -t lint test build
      # # ------------------------------------------------

      # - name: List of NX affected apps
      #   id: affected-apps
      #   run: |
      #     AFFECTED_APPS=$(pnpm nx show projects --affected --type=app)
      #     echo "affected_apps=$AFFECTED_APPS" >> $GITHUB_ENV

      # - name: "[Client Portal] Build and push"
      #   if: contains(env.affected_apps, 'client-portal')
      #   id: docker-client-portal
      #   uses: clincove-eng/github-actions/boone-docker-gcp-aio@46dce826b06483540d9823b1be031700c84ffac7
      #   with:
      #     service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
      #     workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}
      #     registry: ${{ vars.REGISTRY }}
      #     dockerfile: apps/client-portal/ci/Dockerfile
      #     labelTitle: Client Portal
      #     labelDescription: <TODO>
      #     metadata-context: ${{ steps.release.outputs.release_created && 'git' || 'workflow' }}
      #     raw-tags: ${{ steps.release.outputs.release_created && github.ref_name || '' }}
      #   env:
      #     DOCKER_BUILDKIT: 1

      # - name: "[Admin Portal] Build and push"
      #   if: contains(env.affected_apps, 'admin-portal')
      #   id: docker-admin-portal
      #   uses: clincove-eng/github-actions/boone-docker-gcp-aio@46dce826b06483540d9823b1be031700c84ffac7
      #   with:
      #     service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
      #     workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}
      #     registry: ${{ vars.REGISTRY }}
      #     dockerfile: apps/admin-portal/ci/Dockerfile
      #     labelTitle: Clincove Admin Portal
      #     labelDescription: <TODO>
      #     metadata-context: ${{ steps.release.outputs.release_created && 'git' || 'workflow' }}
      #     raw-tags: ${{ steps.release.outputs.release_created && github.ref_name || '' }}
      #   env:
      #     DOCKER_BUILDKIT: 1

      # - name: Parse Meta
      #   id: parse-meta
      #   run: |
      #     IMAGE_LIST=$(jq -r '.["image.name"]' <<< '${{ steps.docker-client-portal.outputs.docker-metadata }}' | tr ',' '\n')
      #     IMAGE_LIST+=$(jq -r '.["image.name"]' <<< '${{ steps.docker-admin-portal.outputs.docker-metadata }}' | tr ',' '\n')
      #     {
      #       echo 'images<<EOF'
      #       echo "$IMAGE_LIST"
      #       echo EOF
      #     } >> "$GITHUB_OUTPUT"

      # - name: Update release
        # if: ${{ steps.release.outputs.tag_name }}
        # id: update_release
        # uses: tubone24/update_release@c04c17054b939144ec8a7cba969d74992f812d66 # v1.3.1
        # env:
        #   GITHUB_TOKEN: ${{ secrets.github_token }}
        #   TAG_NAME: ${{ steps.release.outputs.tag_name }}
        # with:
        #   is_append_body: true
        #   body: |

        #     ### Container Images

        #     Tags:

        #     ```text
        #     ${{ steps.parse-meta.outputs.images }}
        #     ```

        #     Metadata:

        #     ```json
        #     ${{ steps.docker-client-portal.outputs.docker-metadata }}
        #     ${{ steps.docker-admin-portal.outputs.docker-metadata }}
        #     ```