name: Client Portal - Development Environment

on: workflow_dispatch

env:
  SLACK_WEBHOOK_URL: ${{ secrets.ORG_SLACK_WEBHOOK_URL }}
  VERSION: ${{ github.ref_type == 'branch' && github.sha || github.ref_name }}
  SESSION_TIMEOUT: "60"
  INACTIVTY_TIMEOUT_MINUTES: "30"

jobs:
  us-deploy:
    runs-on: self-hosted
    if: ${{ github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/develop' }}
    environment:
      name: development-client-portal
    permissions:
      contents: "read"
      id-token: "write"
    env:
      IMAGE_REPOSITORY: ${{ vars.REGISTRY }}/${{ vars.CLIENT_PORTAL_IMAGE_NAME }}-${{ vars.US_COUNTRY }}
      REPLICAS: ${{ vars.US_REPLICAS }}
      CPU: ${{ vars.US_CPU }}
      RAM: ${{ vars.US_RAM }}
      API_URL: ${{ vars.US_API_URL }}
      COUNTRY: ${{ vars.US_COUNTRY }}
      APP_ENV: ${{ vars.APP_ENV }}
    steps:
      - id: checkout-repo
        name: Checkout repository
        uses: actions/checkout@v4

      - uses: clincove-eng/github-actions/setup-docker@v2
        with:
          service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
          workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}
          registry: ${{ secrets.ORG_ARTIFACT_REGISTRY_BASE_URL }}

      - id: prepare-env-vars
        name: Prepare environment variables
        shell: bash
        run: |
          echo "NEXT_PUBLIC_API_URL=$API_URL" > .env
          echo "NEXT_PUBLIC_COUNTRY=$COUNTRY" >> .env
          echo "NEXT_PUBLIC_INACTIVITY_TIMEOUT=$INACTIVTY_TIMEOUT_MINUTES" >> .env
          echo "SESSION_TIMEOUT=$SESSION_TIMEOUT" >> .env
          echo "NEXT_PUBLIC_APP_ENV=$APP_ENV" >> .env
          echo "NEXT_PUBLIC_AIRTABLE_BASE_ID=$AIRTABLE_BASE_ID" >> .env
          echo "NEXT_PUBLIC_AIRTABLE_TABLE_ID=$AIRTABLE_TABLE_ID" >> .env
          echo "NEXT_PUBLIC_AIRTABLE_ACCESS_TOKEN=$AIRTABLE_ACCESS_TOKEN" >> .env
          echo "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_PUBLISHABLE_KEY }}" >> .env
          echo "CLERK_SECRET_KEY=${{ secrets.CLERK_SECRET_KEY }}" >> .env
          echo "NEXT_PUBLIC_GHOST_API_URL=${{ vars.GHOST_API_URL }}" >> .env
          echo "NEXT_PUBLIC_GHOST_CONTENT_API_KEY=${{ secrets.GHOST_CONTENT_API_KEY }}" >> .env
          echo "NEXT_PUBLIC_POSTHOG_HOST=${{ vars.POSTHOG_HOST }}" >> .env
          echo "NEXT_PUBLIC_POSTHOG_API_KEY=${{ secrets.POSTHOG_API_KEY }}" >> .env

      - uses: clincove-eng/github-actions/build-image@v2
        with:
          image_name: ${{ vars.CLIENT_PORTAL_IMAGE_NAME }}-${{ vars.US_COUNTRY }}
          registry: ${{ vars.REGISTRY }}
          github_token: ${{ secrets.ORG_GITHUB_TOKEN }}
          file: apps/client-portal/ci/Dockerfile

      - id: replace-template-vars
        name: Replace template variables for helm
        run: envsubst < apps/client-portal/ci/values.tpl.yml > values.yml

      - uses: clincove-eng/github-actions/deploy-image@v2
        with:
          application_type: ${{ vars.CLIENT_PORTAL_APPLICATION_TYPE }}
          application_name: ${{ vars.APPLICATION_NAME }}-${{ vars.US_COUNTRY }}
          cluster_name: ${{ vars.US_CLUSTER_NAME }}
          location: ${{ vars.US_CLUSTER_LOCATION }}
          country: ${{ vars.US_COUNTRY }}
          notification_script_path: ./.github/notification/slack.sh

  ca-deploy:
    runs-on: self-hosted
    if: ${{ github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/develop' }}
    environment:
      name: development-client-portal
    permissions:
      contents: "read"
      id-token: "write"
    env:
      IMAGE_REPOSITORY: ${{ vars.REGISTRY }}/${{ vars.CLIENT_PORTAL_IMAGE_NAME }}-${{ vars.CA_COUNTRY }}
      REPLICAS: ${{ vars.CA_REPLICAS }}
      CPU: ${{ vars.CA_CPU }}
      RAM: ${{ vars.CA_RAM }}
      API_URL: ${{ vars.CA_API_URL }}
      COUNTRY: ${{ vars.CA_COUNTRY }}
      APP_ENV: ${{ vars.APP_ENV }}
    steps:
      - id: checkout-repo
        name: Checkout repository
        uses: actions/checkout@v4

      - uses: clincove-eng/github-actions/setup-docker@v2
        with:
          service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
          workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}
          registry: ${{ secrets.ORG_ARTIFACT_REGISTRY_BASE_URL }}

      - id: prepare-env-vars
        name: Prepare environment variables
        shell: bash
        run: |
          echo "NEXT_PUBLIC_API_URL=$API_URL" > .env
          echo "NEXT_PUBLIC_COUNTRY=$COUNTRY" >> .env
          echo "NEXT_PUBLIC_INACTIVITY_TIMEOUT=$INACTIVTY_TIMEOUT_MINUTES" >> .env
          echo "SESSION_TIMEOUT=$SESSION_TIMEOUT" >> .env
          echo "NEXT_PUBLIC_APP_ENV=$APP_ENV" >> .env
          echo "NEXT_PUBLIC_AIRTABLE_BASE_ID=$AIRTABLE_BASE_ID" >> .env
          echo "NEXT_PUBLIC_AIRTABLE_TABLE_ID=$AIRTABLE_TABLE_ID" >> .env
          echo "NEXT_PUBLIC_AIRTABLE_ACCESS_TOKEN=$AIRTABLE_ACCESS_TOKEN" >> .env
          echo "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_PUBLISHABLE_KEY }}" >> .env
          echo "CLERK_SECRET_KEY=${{ secrets.CLERK_SECRET_KEY }}" >> .env
          echo "NEXT_PUBLIC_GHOST_API_URL=${{ vars.GHOST_API_URL }}" >> .env
          echo "NEXT_PUBLIC_GHOST_CONTENT_API_KEY=${{ secrets.GHOST_CONTENT_API_KEY }}" >> .env
          echo "NEXT_PUBLIC_POSTHOG_HOST=${{ vars.POSTHOG_HOST }}" >> .env
          echo "NEXT_PUBLIC_POSTHOG_API_KEY=${{ secrets.POSTHOG_API_KEY }}" >> .env

      - uses: clincove-eng/github-actions/build-image@v2
        with:
          image_name: ${{ vars.CLIENT_PORTAL_IMAGE_NAME }}-${{ vars.CA_COUNTRY }}
          registry: ${{ vars.REGISTRY }}
          github_token: ${{ secrets.ORG_GITHUB_TOKEN }}
          file: apps/client-portal/ci/Dockerfile

      - id: replace-template-vars
        name: Replace template variables for helm
        run: envsubst < apps/client-portal/ci/values.tpl.yml > values.yml

      - uses: clincove-eng/github-actions/deploy-image@v2
        with:
          application_type: ${{ vars.CLIENT_PORTAL_APPLICATION_TYPE }}
          application_name: ${{ vars.APPLICATION_NAME }}-${{ vars.CA_COUNTRY }}
          cluster_name: ${{ vars.CA_CLUSTER_NAME }}
          location: ${{ vars.CA_CLUSTER_LOCATION }}
          country: ${{ vars.CA_COUNTRY }}
          notification_script_path: ./.github/notification/slack.sh
