---
description:
globs:
alwaysApply: true
---

# State Management Guidelines

## Core Libraries
- Use Zustand for global state management
- Use TanStack React Query for server state management
- Use React Context for component tree state sharing
- Use React Hook Form for form state

## Zustand Stores
- Create small, focused stores
- Use TypeScript for store types
- Implement proper selectors for performance
- Follow the store naming convention: `use{Feature}Store`
- Separate actions from state in store definitions
- Use immer for complex state updates when needed

## React Query
- Use proper query keys for caching
- Implement proper error handling
- Use suspense mode when appropriate
- Implement proper retry logic
- Use query invalidation appropriately
- Implement optimistic updates for mutations
- Use prefetching for better UX
- Structure query keys hierarchically

## URL State
- Use nuqs for query string interactions
- Keep URL state in sync with application state
- Use URL state for shareable UI states
- Implement proper URL state serialization/deserialization

## Context
- Keep context providers small and focused
- Avoid unnecessary re-renders in context providers
- Use context selectors when possible
- Combine related contexts when appropriate
- Document context usage and purpose

## Local Component State
- Use useState for simple component state
- Use useReducer for complex component state
- Lift state up when needed
- Avoid redundant state
- Derive state when possible instead of duplicating

## Performance Considerations
- Avoid unnecessary re-renders
- Use proper memoization techniques
- Implement proper data normalization
- Use optimistic updates for better UX
- Implement proper loading states
