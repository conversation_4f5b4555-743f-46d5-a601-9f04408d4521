---
description: Generate commit message
globs:
alwaysApply: false
---
# Git Commit Message Rules

## Commit Message Format

```
<type>(<scope>): <subject>

<body>

<footer>
```

## Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc)
- **refactor**: Code refactoring
- **perf**: Performance improvements
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

## Scope

- **admin**: Admin portal changes
- **client**: Client portal changes
- **shared**: Shared package changes
- **deps**: Dependency updates
- **config**: Configuration changes

## Subject Rules

- Use imperative mood ("add" not "added" or "adds")
- Don't capitalize first letter
- No period at the end
- Maximum 72 characters

## Examples

```
feat(admin): add user management interface

- Add user list view
- Add user creation form
- Add user edit functionality

Closes #123
```

```
fix(client): resolve document preview loading issue

- Fix infinite loading state
- Add error handling
- Improve performance

Fixes #456
```

```
refactor(shared): improve form component structure

- Extract common logic
- Improve type definitions
- Enhance reusability

Related to #789
```

## Branch Naming

- Feature: `feature/ABC-123-feature-name`
- Bugfix: `bugfix/ABC-123-bug-description`
- Hotfix: `hotfix/ABC-123-issue-description`
- Release: `release/v1.2.3`

## Pull Request Guidelines

1. Reference ticket number in title
2. Add meaningful description
3. Include testing instructions
4. List breaking changes
5. Add screenshots for UI changes

# Git Workflow Guidelines

## Branch Naming

- Use kebab-case for branch names
- Use prefixes for branch types:
  - `feature/` for new features
  - `bugfix/` for bug fixes
  - `hotfix/` for urgent fixes
  - `release/` for release branches
  - `docs/` for documentation changes
- Include ticket number if applicable: `feature/ABC-123-user-profile`

## Commit Messages

- Use conventional commit format: `type(scope): message`
- Keep first line under 72 characters
- Use present tense ("add feature" not "added feature")
- Be descriptive about what changed
- Reference ticket numbers in commit body

```
feat(user): add profile image upload

- Add file upload component
- Implement image cropping
- Add avatar preview

Resolves ABC-123
```

## Common Types for Commits

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, semicolons, etc)
- `refactor`: Code changes that neither fix bugs nor add features
- `perf`: Performance improvements
- `test`: Adding or fixing tests
- `chore`: Changes to build process, dependencies, etc.

## Pull Requests

- Create descriptive PR titles
- Include description of changes
- Reference ticket numbers
- Add steps to test the changes
- Request appropriate reviewers
- Keep PRs focused on a single change
- Use draft PRs for work in progress

## Code Reviews

- Review code within 24 hours if possible
- Provide constructive feedback
- Look for:
  - Code quality
  - Test coverage
  - Performance issues
  - Security concerns
  - Documentation
- Use suggestions for small changes
- Discuss larger issues outside the PR

## Merging Strategy

- Use squash merging for feature branches
- Preserve merge commits for release branches
- Delete branches after merging
- Ensure CI passes before merging
- Ensure code review approval before merging

## Release Process

- Use semantic versioning (MAJOR.MINOR.PATCH)
- Create release branches for planned releases
- Tag releases in git
- Write meaningful release notes
- Document breaking changes

## Git Hooks

- Use Husky for git hooks
- Run linters on pre-commit
- Run tests on pre-push
- Validate commit message format
- Ensure code formatting before commit
