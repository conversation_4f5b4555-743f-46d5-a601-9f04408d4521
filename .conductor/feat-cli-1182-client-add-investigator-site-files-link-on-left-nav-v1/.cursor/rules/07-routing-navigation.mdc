---
description:
globs:
alwaysApply: true
---

# Routing and Navigation Guidelines

## Next.js App Router
- Use Next.js App Router for routing
- Follow the Next.js file-based routing conventions
- Implement proper layouts and templates
- Use proper loading and error states
- Implement proper metadata for SEO

## Navigation
- Use Next.js Link component for internal navigation
- Use proper anchor tags for external links
- Implement proper navigation guards
- Handle authentication in navigation
- Implement proper breadcrumbs
- Show active navigation items

## Layouts
- Create reusable layout components
- Implement proper layout composition
- Use Next.js layout system
- Handle responsive layouts properly
- Implement proper navigation in layouts

## Route Parameters
- Use proper route parameter naming
- Validate route parameters
- Handle missing or invalid parameters
- Use proper TypeScript types for route parameters

## Query Parameters
- Use nuqs for query string interactions
- Implement proper query parameter validation
- Handle missing or invalid query parameters
- Use proper TypeScript types for query parameters

## Loading States
- Implement proper loading states for routes
- Use Next.js loading.tsx for route loading
- Show appropriate loading indicators
- Avoid layout shifts during loading

## Error Handling
- Implement proper error boundaries
- Use Next.js error.tsx for route errors
- Show appropriate error messages
- Implement proper fallbacks for errors
- Log errors for debugging

## Authentication and Authorization
- Implement proper authentication checks
- Use middleware for route protection
- Handle unauthorized access properly
- Redirect to login when needed
- Implement proper role-based access control

## Internationalization
- Use next-intl for internationalization
- Implement proper locale detection
- Handle locale in routes
- Implement proper translations
- Support RTL languages when needed

## SEO
- Implement proper metadata for SEO
- Use Next.js metadata API
- Implement proper Open Graph tags
- Implement proper structured data
- Ensure proper canonical URLs
