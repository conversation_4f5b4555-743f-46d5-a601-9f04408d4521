---
description: Guidelines for documentation
globs:
alwaysApply: false
---

# Documentation Guidelines

## Code Documentation

- Use JSDoc for documenting functions, components, and types
- Document complex business logic
- Document non-obvious code decisions
- Document API interfaces
- Document component props

## JSDoc Format

```tsx
/**
 * Component description
 *
 * @param props - Props description
 * @param props.prop1 - Prop1 description
 * @param props.prop2 - Prop2 description
 * @returns Component
 *
 * @example
 * ```tsx
 * <MyComponent prop1="value" prop2={42} />
 * ```
 */
export const MyComponent = ({ prop1, prop2 }: MyComponentProps) => {
  // Component implementation
}
```

## README Files

- Create a README.md for each application
- Include setup instructions
- Document available scripts
- Explain the project structure
- Document key features
- Include troubleshooting tips

## API Documentation

- Document all API endpoints
- Document request and response formats
- Document error codes and messages
- Document authentication requirements
- Keep API documentation up-to-date

## Component Documentation

- Document component props
- Document component usage
- Document component variants
- Include examples
- Document accessibility considerations

## Architecture Documentation

- Document high-level architecture
- Document data flow
- Document state management
- Document routing
- Document key design decisions

## Type Documentation

- Document complex types
- Document type parameters
- Document type constraints
- Document type usages
- Keep type documentation up-to-date

## Development Practices

- Update documentation with code changes
- Review documentation during code reviews
- Keep documentation up-to-date
- Document breaking changes
- Document deprecations
