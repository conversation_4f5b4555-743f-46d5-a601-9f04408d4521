---
description: Accessibility guidelines
globs:
alwaysApply: false
---

# Accessibility Guidelines

## Core Principles

- Follow WCAG 2.1 AA standards
- Design for keyboard accessibility
- Ensure proper focus management
- Provide proper text alternatives
- Use appropriate semantic HTML
- Test with screen readers

## Semantic HTML

- Use proper HTML elements for their intended purpose
- Use proper heading structure (h1-h6)
- Use `<button>` for clickable actions
- Use `<a>` for navigation links
- Use `<label>` for form inputs
- Use landmark elements (`<main>`, `<nav>`, `<header>`, etc.)

## Focus Management

- Ensure all interactive elements are focusable
- Provide a visible focus indicator
- Manage focus during modal dialogs
- Implement proper tab order
- Ensure keyboard traps are avoided
- Implement skip links for navigation

## Forms

- Associate labels with form controls
- Provide clear error messages
- Use appropriate input types
- Implement proper form validation
- Ensure form controls have accessible names
- Group related form controls with fieldset and legend

## Images and Media

- Provide alt text for images
- Use proper captions for media
- Ensure media has proper controls
- Implement proper transcripts for audio
- Ensure videos have proper captions
- Avoid content that flashes or flickers

## Color and Contrast

- Ensure sufficient color contrast (minimum 4.5:1 for normal text)
- Don't rely on color alone to convey information
- Provide visual alternatives for color-based information
- Test with color vision deficiency simulators
- Use proper focus indicators
- Implement proper dark mode support

## ARIA

- Use ARIA attributes when necessary
- Don't override native semantics with ARIA
- Ensure ARIA attributes are updated appropriately
- Test ARIA implementations with screen readers
- Use proper ARIA landmark roles
- Implement proper ARIA live regions for dynamic content

## Testing

- Test with keyboard navigation
- Test with screen readers (NVDA, JAWS, VoiceOver)
- Use automated accessibility testing tools
- Perform manual accessibility audits
- Test with different zoom levels
- Test with different color contrast settings

## Common Components

- Ensure modal dialogs are accessible
- Implement accessible dropdown menus
- Ensure tables have proper headers
- Implement accessible tooltips
- Ensure form controls have accessible error states
- Implement accessible tabs and accordions
